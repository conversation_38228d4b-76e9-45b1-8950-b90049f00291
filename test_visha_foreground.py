#!/usr/bin/env python3
"""
Visha前台检测测试脚本
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from pages.base.app_detector import AppDetector

def test_visha_foreground():
    """测试Visha前台检测"""
    detector = AppDetector()
    
    print("=" * 60)
    print("🎵 Visha前台检测专项测试")
    print("=" * 60)
    
    # 1. 检查Visha应用是否运行（包括后台）
    print("1. 检查Visha应用运行状态（包括后台）...")
    running_status = detector.check_visha_app_running()
    print(f"   结果: {running_status}")
    
    # 2. 检查Visha应用是否在前台
    print("\n2. 检查Visha应用前台状态...")
    foreground_status = detector.check_visha_app_in_foreground()
    print(f"   结果: {foreground_status}")
    
    # 3. 获取Visha包名
    print("\n3. 获取Visha包名...")
    visha_package = detector.get_visha_package_name()
    print(f"   包名: {visha_package if visha_package else '未找到'}")
    
    # 4. 如果找到包名，直接测试简单前台检测
    if visha_package:
        print(f"\n4. 直接测试简单前台检测方法...")
        simple_foreground = detector._simple_foreground_check(visha_package)
        print(f"   简单前台检测结果: {simple_foreground}")
        
        print(f"\n5. 直接测试严格前台检测方法...")
        strict_foreground = detector._verify_app_strictly_in_foreground(visha_package)
        print(f"   严格前台检测结果: {strict_foreground}")
    
    # 6. 状态总结
    print("\n" + "=" * 60)
    print("📊 检测结果总结:")
    print(f"   运行状态（包括后台）: {running_status}")
    print(f"   前台状态: {foreground_status}")
    
    if running_status:
        if foreground_status:
            print("   ✅ Visha应用正在前台运行")
        else:
            print("   🔄 Visha应用在后台运行")
    else:
        print("   ❌ Visha应用未运行")
    
    print("=" * 60)

if __name__ == "__main__":
    test_visha_foreground()
