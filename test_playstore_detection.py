#!/usr/bin/env python3
"""
测试Google Play Store检测功能 - 严格前台检测版本
"""
import sys
import os
import subprocess
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pages.base.app_detector import AppDetector
from core.logger import log


def get_current_foreground_app():
    """获取当前前台应用"""
    try:
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "window", "windows"],
            capture_output=True,
            text=True,
            timeout=5
        )

        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for line in lines:
                if "mCurrentFocus=" in line:
                    return line.strip()
        return "未知"
    except Exception as e:
        return f"获取失败: {e}"


def test_playstore_detection():
    """测试Google Play Store检测功能"""
    print("=" * 70)
    print("测试Google Play Store应用严格前台检测功能")
    print("=" * 70)

    detector = AppDetector()

    try:
        # 显示当前前台应用
        current_app = get_current_foreground_app()
        print(f"\n📱 当前前台应用: {current_app}")

        # 测试Google Play Store检测
        print("\n🔍 开始严格检测Google Play Store应用前台状态...")
        result = detector.check_google_playstore_app_opened()

        if result:
            print("✅ 检测结果: Google Play Store应用正在前台运行")
        else:
            print("❌ 检测结果: Google Play Store应用未在前台运行")

        print(f"\n📊 最终检测结果: {result}")

        # 额外验证：手动检查焦点窗口
        print("\n🔍 手动验证当前焦点窗口...")
        try:
            result_manual = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result_manual.returncode == 0:
                lines = result_manual.stdout.split('\n')
                for line in lines:
                    if "mCurrentFocus=" in line:
                        print(f"🎯 当前焦点: {line.strip()}")

                        # 检查是否包含Play Store包名
                        playstore_packages = ["com.android.vending", "com.google.android.finsky"]
                        is_playstore_focused = any(pkg in line for pkg in playstore_packages)
                        print(f"🎯 是否为Play Store焦点: {is_playstore_focused}")
                        break
            else:
                print("❌ 无法获取焦点窗口信息")

        except Exception as e:
            print(f"❌ 手动验证失败: {e}")

        # 测试建议
        print("\n💡 测试建议:")
        if result:
            print("   - 如果Play Store实际未打开，请检查检测逻辑")
            print("   - 验证是否有Play Store后台服务在运行")
        else:
            print("   - 如果Play Store实际已打开，请检查包名是否正确")
            print("   - 尝试手动打开Play Store后重新测试")

    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        log.error(f"测试失败: {e}")
        return False

    print("\n" + "=" * 70)
    print("测试完成")
    print("=" * 70)
    return True


def test_with_playstore_operations():
    """测试Play Store操作前后的检测结果"""
    print("\n" + "=" * 70)
    print("测试Play Store操作前后的检测变化")
    print("=" * 70)

    detector = AppDetector()

    try:
        # 测试1: 当前状态
        print("\n📊 测试1: 当前状态检测")
        result1 = detector.check_google_playstore_app_opened()
        print(f"   结果: {result1}")

        # 测试2: 尝试启动Play Store
        print("\n📊 测试2: 尝试启动Play Store")
        try:
            subprocess.run(
                ["adb", "shell", "am", "start", "-n", "com.android.vending/.AssetBrowserActivity"],
                capture_output=True,
                text=True,
                timeout=10
            )
            print("   已发送启动命令")

            # 等待应用启动
            time.sleep(3)

            result2 = detector.check_google_playstore_app_opened()
            print(f"   启动后检测结果: {result2}")

        except Exception as e:
            print(f"   启动失败: {e}")

        # 测试3: 返回桌面
        print("\n📊 测试3: 返回桌面后检测")
        try:
            subprocess.run(
                ["adb", "shell", "input", "keyevent", "KEYCODE_HOME"],
                capture_output=True,
                text=True,
                timeout=5
            )
            print("   已返回桌面")

            # 等待切换完成
            time.sleep(2)

            result3 = detector.check_google_playstore_app_opened()
            print(f"   返回桌面后检测结果: {result3}")

        except Exception as e:
            print(f"   返回桌面失败: {e}")

    except Exception as e:
        print(f"❌ 操作测试失败: {e}")


if __name__ == "__main__":
    test_playstore_detection()
    test_with_playstore_operations()
