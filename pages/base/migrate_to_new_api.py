"""
将所有旧版本的应用检测方法改造为推荐的新写法
detector.check_app_opened(AppType.WEATHER) 替代 detector.check_weather_app_opened()
"""
import os
import re
import glob
from pathlib import Path

# 旧方法到新方法的映射
OLD_TO_NEW_MAPPING = {
    'check_weather_app_opened()': 'check_app_opened(AppType.WEATHER)',
    'check_camera_app_opened()': 'check_app_opened(AppType.CAMERA)',
    'check_settings_opened()': 'check_app_opened(AppType.SETTINGS)',
    'check_contacts_app_opened()': 'check_app_opened(AppType.CONTACTS)',
    'check_facebook_app_opened()': 'check_app_opened(AppType.FACEBOOK)',
    'check_music_app_opened()': 'check_app_opened(AppType.MUSIC)',
    'check_clock_app_opened()': 'check_app_opened(AppType.CLOCK)',
    'check_google_map_app_opened()': 'check_app_opened(AppType.MAPS)',
    'check_google_playstore_app_opened()': 'check_app_opened(AppType.PLAYSTORE)',
    'check_visha_app_opened()': 'check_app_opened(AppType.MUSIC)',
    'check_visha_app_in_foreground()': 'check_app_in_foreground(AppType.MUSIC)',
    'check_visha_app_running()': 'check_app_opened(AppType.MUSIC)',
}

# 需要添加的导入语句
REQUIRED_IMPORTS = [
    'from pages.base.app_detector import AppDetector, AppType',
    'from app_detector import AppDetector, AppType',  # 相对导入版本
]

def find_python_files(root_dir):
    """查找所有Python文件"""
    python_files = []
    for root, dirs, files in os.walk(root_dir):
        # 跳过一些不需要的目录
        dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    return python_files

def check_needs_apptype_import(content):
    """检查是否需要导入AppType"""
    # 检查是否使用了AppType
    if 'AppType.' in content:
        # 检查是否已经导入了AppType
        if 'from pages.base.app_detector import' in content and 'AppType' in content:
            return False
        if 'from app_detector import' in content and 'AppType' in content:
            return False
        return True
    return False

def add_apptype_import(content, file_path):
    """添加AppType导入"""
    lines = content.split('\n')
    
    # 查找合适的位置插入导入
    import_line_index = -1
    for i, line in enumerate(lines):
        if line.strip().startswith('from pages.base.app_detector import'):
            # 更新现有导入
            if 'AppType' not in line:
                lines[i] = line.replace('AppDetector', 'AppDetector, AppType')
            return '\n'.join(lines)
        elif line.strip().startswith('from app_detector import'):
            # 更新现有导入
            if 'AppType' not in line:
                lines[i] = line.replace('AppDetector', 'AppDetector, AppType')
            return '\n'.join(lines)
        elif line.strip().startswith('import') or line.strip().startswith('from'):
            import_line_index = i
    
    # 如果没有找到现有的app_detector导入，添加新的
    if import_line_index >= 0:
        # 根据文件路径决定使用哪种导入方式
        if 'pages/base/' in file_path.replace('\\', '/'):
            new_import = 'from app_detector import AppDetector, AppType'
        else:
            new_import = 'from pages.base.app_detector import AppDetector, AppType'
        
        lines.insert(import_line_index + 1, new_import)
    
    return '\n'.join(lines)

def migrate_file(file_path):
    """迁移单个文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = []
        
        # 检查是否包含需要迁移的方法
        has_old_methods = False
        for old_method in OLD_TO_NEW_MAPPING.keys():
            if old_method in content:
                has_old_methods = True
                break
        
        if not has_old_methods:
            return False, []
        
        print(f"\n🔄 迁移文件: {file_path}")
        
        # 替换方法调用
        for old_method, new_method in OLD_TO_NEW_MAPPING.items():
            # 匹配各种调用模式
            patterns = [
                rf'\.{re.escape(old_method)}',  # .check_weather_app_opened()
                rf'{re.escape(old_method)}',    # check_weather_app_opened()
            ]
            
            for pattern in patterns:
                if re.search(pattern, content):
                    # 替换方法调用
                    content = re.sub(pattern, f'.{new_method}' if pattern.startswith(r'\.') else new_method, content)
                    changes_made.append(f"  ✅ {old_method} → {new_method}")
        
        # 检查是否需要添加AppType导入
        if check_needs_apptype_import(content):
            content = add_apptype_import(content, file_path)
            changes_made.append("  ✅ 添加 AppType 导入")
        
        # 如果有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"  📝 变更内容:")
            for change in changes_made:
                print(change)
            
            return True, changes_made
        
        return False, []
        
    except Exception as e:
        print(f"❌ 迁移文件失败 {file_path}: {e}")
        return False, []

def create_migration_summary(migrated_files, total_changes):
    """创建迁移摘要"""
    summary = f"""
# 应用检测器API迁移摘要

## 📊 迁移统计
- 迁移文件数: {len(migrated_files)}
- 总变更数: {total_changes}

## 📝 迁移的文件
"""
    
    for file_path, changes in migrated_files:
        summary += f"\n### {file_path}\n"
        for change in changes:
            summary += f"{change}\n"
    
    summary += f"""
## 🎯 迁移完成

所有旧版本的应用检测方法已成功迁移为推荐的新写法：

### 新API优势
- ✅ 统一的接口设计
- ✅ 类型安全的枚举
- ✅ 更好的代码可读性
- ✅ 易于维护和扩展

### 使用示例
```python
from pages.base.app_detector import AppDetector, AppType

detector = AppDetector()

# 推荐的新写法
detector.check_app_opened(AppType.WEATHER)
detector.check_app_opened(AppType.CAMERA)
detector.check_app_opened(AppType.MUSIC)

# 高级功能
summary = detector.get_running_apps_summary()
detector.check_app_in_foreground(AppType.MUSIC)
```
"""
    
    return summary

def main():
    """主迁移函数"""
    print("🚀 开始迁移应用检测器API到推荐的新写法")
    print("=" * 60)
    
    # 获取项目根目录
    current_dir = Path(__file__).parent
    project_root = current_dir.parent.parent
    
    # 查找所有Python文件
    python_files = find_python_files(str(project_root))
    print(f"📁 找到 {len(python_files)} 个Python文件")
    
    migrated_files = []
    total_changes = 0
    
    # 迁移每个文件
    for file_path in python_files:
        # 跳过一些特殊文件
        if any(skip in file_path for skip in ['__pycache__', '.git', 'migrate_to_new_api.py']):
            continue
        
        success, changes = migrate_file(file_path)
        if success:
            migrated_files.append((file_path, changes))
            total_changes += len(changes)
    
    # 生成迁移摘要
    if migrated_files:
        summary = create_migration_summary(migrated_files, total_changes)
        
        # 保存摘要到文件
        summary_file = current_dir / "migration_summary.md"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary)
        
        print(f"\n📋 迁移摘要:")
        print(f"  ✅ 成功迁移 {len(migrated_files)} 个文件")
        print(f"  ✅ 总共 {total_changes} 处变更")
        print(f"  📄 详细摘要保存到: {summary_file}")
    else:
        print("\n💡 没有找到需要迁移的文件")
    
    print("\n" + "=" * 60)
    print("🎉 迁移完成！")
    
    if migrated_files:
        print("\n🎯 迁移后的优势:")
        print("  • 统一的API接口，更易使用")
        print("  • 类型安全的枚举，减少错误")
        print("  • 更好的代码可读性和维护性")
        print("  • 支持更多高级功能")

if __name__ == '__main__':
    main()
