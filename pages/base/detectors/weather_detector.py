"""
天气应用检测器
"""
from typing import List
import sys
import os

# 添加父目录到路径以便导入
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

try:
    from app_detector import BaseAppDetector, AppType
except ImportError:
    # 如果还是失败，尝试相对导入
    from ..app_detector import BaseAppDetector, AppType


class WeatherDetector(BaseAppDetector):
    """天气应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.WEATHER)
    
    def get_package_names(self) -> List[str]:
        """获取天气应用包名列表"""
        return [
            "com.miui.weather",
            "com.android.weather",
            "com.google.android.apps.weather",
            "com.transsion.weather",
            "com.weather.forecast",
            "com.accuweather",
            "com.weather.channel",
        ]
    
    def get_keywords(self) -> List[str]:
        """获取天气应用关键词列表"""
        return ["weather", "clima", "meteo", "天气"]
