"""
时钟应用检测器
"""
from typing import List
from ..app_detector import BaseAppDetector, AppType


class ClockDetector(BaseAppDetector):
    """时钟应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.CLOCK)
    
    def get_package_names(self) -> List[str]:
        """获取时钟应用包名列表"""
        return [
            "com.transsion.deskclock",
            "com.android.deskclock",
            "com.google.android.deskclock",
            "com.samsung.android.app.clockpackage",
            "com.huawei.deskclock",
            "com.xiaomi.deskclock",
            "com.oppo.clock",
            "com.vivo.clock",
        ]
    
    def get_keywords(self) -> List[str]:
        """获取时钟应用关键词列表"""
        return ["clock", "time", "alarm", "timer", "deskclock", "时钟", "闹钟"]
