"""
设置应用检测器
"""
from typing import List
from ..app_detector import BaseAppDetector, AppType


class SettingsDetector(BaseAppDetector):
    """设置应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.SETTINGS)
    
    def get_package_names(self) -> List[str]:
        """获取设置应用包名列表"""
        return [
            "com.android.settings",
            "com.transsion.settings",
            "com.sec.android.app.settings",
            "com.huawei.android.settings",
            "com.xiaomi.misettings",
            "com.oppo.settings",
            "com.vivo.settings",
            "com.oneplus.settings",
            "com.coloros.settings",
            "com.miui.securitycenter",
        ]
    
    def get_keywords(self) -> List[str]:
        """获取设置应用关键词列表"""
        return ["settings", "setting", "设置", "配置"]
