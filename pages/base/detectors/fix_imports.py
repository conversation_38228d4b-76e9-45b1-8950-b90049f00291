"""
修复检测器文件的导入问题
"""
import os
import glob

def fix_detector_imports():
    """修复所有检测器文件的导入"""
    
    # 通用的导入修复模板
    import_fix = '''import sys
import os

# 添加父目录到路径以便导入
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

try:
    from app_detector import BaseAppDetector, AppType
except ImportError:
    # 如果还是失败，尝试相对导入
    from ..app_detector import BaseAppDetector, AppType'''
    
    # 获取所有检测器文件
    detector_files = glob.glob("*_detector.py")
    detector_files = [f for f in detector_files if f != "alarm_detector.py"]  # 闹钟检测器不需要BaseAppDetector
    
    for file_path in detector_files:
        print(f"修复 {file_path}...")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找并替换导入部分
            lines = content.split('\n')
            new_lines = []
            skip_until_class = False
            
            for line in lines:
                if line.startswith('"""') and not skip_until_class:
                    new_lines.append(line)
                elif '"""' in line and not skip_until_class:
                    new_lines.append(line)
                    new_lines.append('from typing import List')
                    new_lines.append(import_fix)
                    new_lines.append('')
                    skip_until_class = True
                elif skip_until_class and line.startswith('class'):
                    new_lines.append(line)
                    skip_until_class = False
                elif not skip_until_class:
                    continue  # 跳过原有的导入
                else:
                    new_lines.append(line)
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(new_lines))
            
            print(f"✅ {file_path} 修复完成")
            
        except Exception as e:
            print(f"❌ {file_path} 修复失败: {e}")

if __name__ == '__main__':
    print("🔧 开始修复检测器导入问题...")
    fix_detector_imports()
    print("✅ 修复完成！")
