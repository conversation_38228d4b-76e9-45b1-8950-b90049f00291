"""
闹钟检测器
提供闹钟相关的检测和管理功能
"""
import re
import subprocess
from typing import List, Dict, Optional
from core.logger import log
from .detector_utils import DetectorUtils


class AlarmDetector:
    """闹钟检测器"""
    
    def __init__(self):
        self.timeout = 15
    
    def check_alarm_status(self) -> bool:
        """
        检查闹钟状态
        
        Returns:
            bool: 是否有设置的闹钟
        """
        try:
            log.info("检查闹钟状态")
            
            success, alarm_output = DetectorUtils.execute_adb_command(
                ["adb", "shell", "dumpsys", "alarm"], timeout=self.timeout
            )
            
            if success:
                # 检查是否包含闹钟相关信息
                alarm_indicators = [
                    "com.transsion.deskclock",
                    "AlarmManager",
                    "alarm_clock",
                    "RTC_WAKEUP"
                ]
                
                for indicator in alarm_indicators:
                    if indicator in alarm_output:
                        log.info(f"检测到闹钟相关信息: {indicator}")
                        return True
                
                log.info("未检测到活跃的闹钟")
                return False
            else:
                log.error("获取闹钟状态失败")
                return False
                
        except Exception as e:
            log.error(f"检查闹钟状态失败: {e}")
            return False
    
    def get_alarm_list(self) -> List[Dict[str, any]]:
        """
        获取闹钟列表
        
        Returns:
            List[Dict]: 闹钟列表
        """
        try:
            log.info("获取闹钟列表")
            alarms = []
            
            success, alarm_output = DetectorUtils.execute_adb_command(
                ["adb", "shell", "dumpsys", "alarm"], timeout=self.timeout
            )
            
            if success:
                alarms.extend(self._parse_alarm_database_output(alarm_output))
            
            log.info(f"找到 {len(alarms)} 个闹钟")
            return alarms
            
        except Exception as e:
            log.error(f"获取闹钟列表失败: {e}")
            return []
    
    def _parse_alarm_database_output(self, output: str) -> List[Dict[str, any]]:
        """
        解析闹钟数据库输出
        
        Args:
            output: 数据库输出内容
            
        Returns:
            List[Dict]: 解析出的闹钟列表
        """
        alarms = []
        try:
            lines = output.split('\n')
            for line in lines:
                line = line.strip()
                # 查找包含时间信息的行
                if any(keyword in line.lower() for keyword in ['alarm', 'clock', 'time']):
                    # 简单的时间模式匹配
                    time_pattern = r'\b([0-1]?[0-9]|2[0-3]):[0-5][0-9]\b'
                    matches = re.findall(time_pattern, line)
                    for match in matches:
                        if match not in [alarm.get('time') for alarm in alarms]:
                            alarms.append({
                                'time': match,
                                'enabled': 'enabled' in line.lower() or 'on' in line.lower(),
                                'source': 'dumpsys'
                            })
        except Exception as e:
            log.debug(f"解析闹钟输出失败: {e}")
        
        return alarms
    
    def clear_all_alarms(self) -> bool:
        """
        清除所有闹钟
        
        Returns:
            bool: 是否成功清除
        """
        try:
            log.info("清除所有闹钟")
            
            # 通过ADB命令清除闹钟
            commands = [
                ["adb", "shell", "am", "broadcast", "-a", "android.intent.action.ALARM_CHANGED"],
                ["adb", "shell", "settings", "put", "system", "alarm_alert", ""],
            ]
            
            success_count = 0
            for cmd in commands:
                try:
                    success, _ = DetectorUtils.execute_adb_command(cmd, timeout=10)
                    if success:
                        success_count += 1
                except Exception as e:
                    log.debug(f"执行清除命令失败: {e}")
            
            success = success_count > 0
            log.info(f"闹钟清除{'成功' if success else '失败'}")
            return success
            
        except Exception as e:
            log.error(f"清除闹钟失败: {e}")
            return False
    
    def set_alarm(self, hour: int, minute: int, enabled: bool = True) -> bool:
        """
        设置闹钟
        
        Args:
            hour: 小时 (0-23)
            minute: 分钟 (0-59)
            enabled: 是否启用
            
        Returns:
            bool: 是否设置成功
        """
        try:
            if not (0 <= hour <= 23) or not (0 <= minute <= 59):
                log.error("无效的时间参数")
                return False
            
            log.info(f"设置闹钟: {hour:02d}:{minute:02d}")
            
            # 使用Intent启动闹钟设置
            success, _ = DetectorUtils.execute_adb_command([
                "adb", "shell", "am", "start",
                "-a", "android.intent.action.SET_ALARM",
                "--ei", "android.intent.extra.alarm.HOUR", str(hour),
                "--ei", "android.intent.extra.alarm.MINUTES", str(minute),
                "--ez", "android.intent.extra.alarm.SKIP_UI", "true"
            ], timeout=10)
            
            if success:
                log.info(f"闹钟设置成功: {hour:02d}:{minute:02d}")
            else:
                log.error("闹钟设置失败")
            
            return success
            
        except Exception as e:
            log.error(f"设置闹钟失败: {e}")
            return False
    
    def get_next_alarm_info(self) -> Optional[Dict[str, any]]:
        """
        获取下一个闹钟信息
        
        Returns:
            Optional[Dict]: 下一个闹钟信息，如果没有则返回None
        """
        try:
            success, output = DetectorUtils.execute_adb_command(
                ["adb", "shell", "settings", "get", "system", "next_alarm_formatted"], 
                timeout=5
            )
            
            if success and output.strip():
                return {
                    'formatted_time': output.strip(),
                    'source': 'system_settings'
                }
            
            # 备用方法：从闹钟列表中找到最近的
            alarms = self.get_alarm_list()
            if alarms:
                # 简单返回第一个找到的闹钟
                return alarms[0]
            
            return None
            
        except Exception as e:
            log.debug(f"获取下一个闹钟信息失败: {e}")
            return None
