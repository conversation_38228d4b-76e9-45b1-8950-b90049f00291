"""
验证API迁移是否正确完成
"""
import os
import re
from pathlib import Path

def find_python_files(root_dir):
    """查找所有Python文件"""
    python_files = []
    for root, dirs, files in os.walk(root_dir):
        # 跳过一些不需要的目录
        dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    return python_files

def check_file_migration(file_path):
    """检查单个文件的迁移状态"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        issues = []
        
        # 检查是否还有旧的方法调用
        old_methods = [
            'check_weather_app_opened()',
            'check_camera_app_opened()',
            'check_settings_opened()',
            'check_contacts_app_opened()',
            'check_facebook_app_opened()',
            'check_music_app_opened()',
            'check_clock_app_opened()',
            'check_google_map_app_opened()',
            'check_google_playstore_app_opened()',
            'check_visha_app_opened()',
        ]
        
        for old_method in old_methods:
            # 检查方法调用（不包括方法定义）
            pattern = rf'\.{re.escape(old_method)}'
            if re.search(pattern, content):
                issues.append(f"仍有旧方法调用: {old_method}")
        
        # 检查是否使用了AppType但没有导入
        if 'AppType.' in content:
            has_import = False
            # 检查各种可能的导入方式
            import_patterns = [
                r'from pages\.base\.app_detector import.*AppType',
                r'from app_detector import.*AppType',
                r'from \.\.app_detector import.*AppType',
            ]

            for pattern in import_patterns:
                if re.search(pattern, content):
                    has_import = True
                    break

            if not has_import:
                issues.append("使用了AppType但没有正确导入")
        
        return issues
        
    except Exception as e:
        return [f"读取文件失败: {e}"]

def main():
    """主验证函数"""
    print("🔍 验证API迁移结果")
    print("=" * 50)
    
    # 获取项目根目录
    current_dir = Path(__file__).parent
    project_root = current_dir.parent.parent
    
    # 查找所有Python文件
    python_files = find_python_files(str(project_root))
    print(f"📁 检查 {len(python_files)} 个Python文件")
    
    total_issues = 0
    files_with_issues = []
    
    # 检查每个文件
    for file_path in python_files:
        # 跳过一些特殊文件
        if any(skip in file_path for skip in ['__pycache__', '.git', 'migrate_to_new_api.py', 'verify_migration.py']):
            continue
        
        issues = check_file_migration(file_path)
        if issues:
            files_with_issues.append((file_path, issues))
            total_issues += len(issues)
    
    # 输出结果
    if files_with_issues:
        print(f"\n❌ 发现 {total_issues} 个问题:")
        for file_path, issues in files_with_issues:
            print(f"\n📄 {file_path}:")
            for issue in issues:
                print(f"  • {issue}")
    else:
        print("\n✅ 所有文件迁移正确！")
    
    # 统计新API使用情况
    print(f"\n📊 新API使用统计:")
    new_api_usage = 0
    files_using_new_api = 0
    
    for file_path in python_files:
        if any(skip in file_path for skip in ['__pycache__', '.git']):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 统计新API使用次数
            new_api_patterns = [
                r'check_app_opened\(AppType\.',
                r'check_app_in_foreground\(AppType\.',
                r'get_running_apps_summary\(\)',
                r'get_app_version\(AppType\.',
            ]
            
            file_usage = 0
            for pattern in new_api_patterns:
                matches = re.findall(pattern, content)
                file_usage += len(matches)
            
            if file_usage > 0:
                new_api_usage += file_usage
                files_using_new_api += 1
                
        except:
            continue
    
    print(f"  • 使用新API的文件: {files_using_new_api} 个")
    print(f"  • 新API调用总数: {new_api_usage} 次")
    
    # 显示迁移前后对比
    print(f"\n📈 迁移对比:")
    print("  旧写法: detector.check_weather_app_opened()")
    print("  新写法: detector.check_app_opened(AppType.WEATHER)")
    print("  优势: ✅ 统一接口 ✅ 类型安全 ✅ 易扩展")
    
    print("\n" + "=" * 50)
    if files_with_issues:
        print("⚠️ 迁移需要进一步完善")
    else:
        print("🎉 迁移验证通过！")

if __name__ == '__main__':
    main()
