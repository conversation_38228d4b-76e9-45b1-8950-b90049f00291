"""
应用检测器 - 重构版本
使用策略模式和工厂模式优化应用检测功能
"""
import subprocess
import sys
import os
from pathlib import Path
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union
from enum import Enum

# 添加项目根目录到路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
sys.path.insert(0, str(project_root))

from core.logger import log


class AppType(Enum):
    """应用类型枚举"""
    WEATHER = "weather"
    CAMERA = "camera"
    SETTINGS = "settings"
    CONTACTS = "contacts"
    FACEBOOK = "facebook"
    MUSIC = "music"
    CLOCK = "clock"
    MAPS = "maps"
    PLAYSTORE = "playstore"


class BaseAppDetector(ABC):
    """应用检测器基类 - 模板方法模式"""
    
    def __init__(self, app_type: AppType):
        self.app_type = app_type
        self.timeout = 10
        
    @abstractmethod
    def get_package_names(self) -> List[str]:
        """获取应用包名列表"""
        pass
    
    @abstractmethod
    def get_keywords(self) -> List[str]:
        """获取应用关键词列表"""
        pass
    
    def check_app_opened(self) -> bool:
        """检查应用是否打开 - 模板方法"""
        try:
            log.info(f"检查{self.app_type.value}应用状态")
            
            # 1. 优先检查活动状态
            if self._check_activity_status():
                return True
                
            # 2. 检查焦点窗口
            if self._check_focus_window():
                return True
                
            # 3. 检查进程状态
            if self._check_process_status():
                return True
                
            log.info(f"未检测到{self.app_type.value}应用")
            return False
            
        except Exception as e:
            log.error(f"检查{self.app_type.value}应用失败: {e}")
            return False
    
    def _check_activity_status(self) -> bool:
        """检查活动状态"""
        try:
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=self.timeout,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0:
                activity_output = result.stdout
                return self._analyze_activity_output(activity_output)
                
        except Exception as e:
            log.debug(f"检查活动状态失败: {e}")
        return False
    
    def _check_focus_window(self) -> bool:
        """检查焦点窗口"""
        try:
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows"],
                capture_output=True,
                text=True,
                timeout=self.timeout,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0:
                window_output = result.stdout
                return self._analyze_window_output(window_output)
                
        except Exception as e:
            log.debug(f"检查焦点窗口失败: {e}")
        return False
    
    def _check_process_status(self) -> bool:
        """检查进程状态"""
        try:
            result = subprocess.run(
                ["adb", "shell", "ps"],
                capture_output=True,
                text=True,
                timeout=self.timeout,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0:
                ps_output = result.stdout
                return self._analyze_process_output(ps_output)
                
        except Exception as e:
            log.debug(f"检查进程状态失败: {e}")
        return False
    
    def _analyze_activity_output(self, output: str) -> bool:
        """分析活动输出"""
        packages = self.get_package_names()
        keywords = self.get_keywords()
        
        # 检查包名
        for package in packages:
            if package in output:
                if self._verify_activity_resumed(output, package):
                    log.info(f"✅ 通过活动检测到{self.app_type.value}应用: {package}")
                    return True
        
        # 检查关键词
        for keyword in keywords:
            if keyword.lower() in output.lower():
                log.info(f"✅ 通过关键词检测到{self.app_type.value}应用: {keyword}")
                return True
                
        return False
    
    def _analyze_window_output(self, output: str) -> bool:
        """分析窗口输出"""
        packages = self.get_package_names()
        
        lines = output.split('\n')
        for line in lines:
            if "mCurrentFocus=" in line:
                for package in packages:
                    if package in line and "null" not in line:
                        log.info(f"✅ 通过焦点窗口检测到{self.app_type.value}应用: {package}")
                        return True
                break
        return False
    
    def _analyze_process_output(self, output: str) -> bool:
        """分析进程输出"""
        packages = self.get_package_names()
        
        lines = output.split('\n')
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            for package in packages:
                if package in line:
                    parts = line.split()
                    if len(parts) >= 8 and any(status in parts for status in ['S', 'R', 'D', 'T']):
                        log.info(f"✅ 通过进程检测到{self.app_type.value}应用: {package}")
                        return True
        return False
    
    def _verify_activity_resumed(self, output: str, package: str) -> bool:
        """验证活动是否处于RESUMED状态"""
        lines = output.split('\n')
        for i, line in enumerate(lines):
            if package in line and "ActivityRecord{" in line:
                # 检查后续几行是否有RESUMED状态
                for j in range(i, min(i + 5, len(lines))):
                    if "state=RESUMED" in lines[j] or "RESUMED" in lines[j]:
                        return True
        return False


class AppDetector:
    """应用检测器主类 - 使用工厂模式"""

    def __init__(self):
        """初始化应用检测器"""
        self._detectors: Dict[AppType, BaseAppDetector] = {}
        self._init_detectors()
    
    def _init_detectors(self):
        """初始化各种应用检测器"""
        # 延迟导入避免循环依赖
        try:
            from .detectors.weather_detector import WeatherDetector
            from .detectors.camera_detector import CameraDetector
            from .detectors.settings_detector import SettingsDetector
            from .detectors.contacts_detector import ContactsDetector
            from .detectors.facebook_detector import FacebookDetector
            from .detectors.music_detector import MusicDetector
            from .detectors.clock_detector import ClockDetector
            from .detectors.maps_detector import MapsDetector
            from .detectors.playstore_detector import PlayStoreDetector
        except ImportError:
            # 如果相对导入失败，尝试绝对导入
            import sys
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            detectors_dir = os.path.join(current_dir, 'detectors')
            if detectors_dir not in sys.path:
                sys.path.insert(0, detectors_dir)

            from weather_detector import WeatherDetector
            from camera_detector import CameraDetector
            from settings_detector import SettingsDetector
            from contacts_detector import ContactsDetector
            from facebook_detector import FacebookDetector
            from music_detector import MusicDetector
            from clock_detector import ClockDetector
            from maps_detector import MapsDetector
            from playstore_detector import PlayStoreDetector
        
        self._detectors = {
            AppType.WEATHER: WeatherDetector(),
            AppType.CAMERA: CameraDetector(),
            AppType.SETTINGS: SettingsDetector(),
            AppType.CONTACTS: ContactsDetector(),
            AppType.FACEBOOK: FacebookDetector(),
            AppType.MUSIC: MusicDetector(),
            AppType.CLOCK: ClockDetector(),
            AppType.MAPS: MapsDetector(),
            AppType.PLAYSTORE: PlayStoreDetector(),
        }
    
    def get_detector(self, app_type: AppType) -> Optional[BaseAppDetector]:
        """获取指定类型的检测器"""
        return self._detectors.get(app_type)
    
    def check_app_opened(self, app_type: Union[AppType, str]) -> bool:
        """检查指定类型的应用是否打开"""
        if isinstance(app_type, str):
            try:
                app_type = AppType(app_type)
            except ValueError:
                log.error(f"不支持的应用类型: {app_type}")
                return False

        detector = self.get_detector(app_type)
        if detector:
            return detector.check_app_opened()
        else:
            log.error(f"未找到{app_type.value}应用的检测器")
            return False

    # ==================== 向后兼容的方法 ====================

    def check_weather_app_opened(self) -> bool:
        """检查天气应用是否打开 - 向后兼容"""
        return self.check_app_opened(AppType.WEATHER)

    def check_camera_app_opened(self) -> bool:
        """检查相机应用是否打开 - 向后兼容"""
        return self.check_app_opened(AppType.CAMERA)

    def check_settings_opened(self) -> bool:
        """检查设置应用是否打开 - 向后兼容"""
        return self.check_app_opened(AppType.SETTINGS)

    def check_contacts_app_opened(self) -> bool:
        """检查联系人应用是否打开 - 向后兼容"""
        return self.check_app_opened(AppType.CONTACTS)

    def check_facebook_app_opened(self) -> bool:
        """检查Facebook应用是否打开 - 向后兼容"""
        return self.check_app_opened(AppType.FACEBOOK)

    def check_music_app_opened(self) -> bool:
        """检查音乐应用是否打开 - 向后兼容"""
        return self.check_app_opened(AppType.MUSIC)

    def check_clock_app_opened(self, clock_package_name: str = "com.transsion.deskclock") -> bool:
        """检查时钟应用是否打开 - 向后兼容"""
        return self.check_app_opened(AppType.CLOCK)

    def check_google_map_app_opened(self) -> bool:
        """检查Google地图应用是否打开 - 向后兼容"""
        return self.check_app_opened(AppType.MAPS)

    def check_google_playstore_app_opened(self) -> bool:
        """检查Google Play Store应用是否打开 - 向后兼容"""
        return self.check_app_opened(AppType.PLAYSTORE)

    def check_visha_app_opened(self, include_background: bool = True) -> bool:
        """检查Visha音乐应用是否打开 - 向后兼容"""
        return self.check_app_opened(AppType.MUSIC)

    def check_visha_app_in_foreground(self) -> bool:
        """检查Visha音乐应用是否在前台 - 向后兼容"""
        return self.check_app_opened(AppType.MUSIC)

    def check_visha_app_running(self) -> bool:
        """检查Visha音乐应用是否正在运行 - 向后兼容"""
        return self.check_app_opened(AppType.MUSIC)

    # ==================== 高级功能方法 ====================

    def check_camera_permission(self) -> bool:
        """检查相机权限状态"""
        try:
            from .detectors.detector_utils import DetectorUtils
        except ImportError:
            import sys
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            detectors_dir = os.path.join(current_dir, 'detectors')
            if detectors_dir not in sys.path:
                sys.path.insert(0, detectors_dir)
            from detector_utils import DetectorUtils

        return DetectorUtils.check_app_permissions(
            "com.transsion.aivoiceassistant",
            "android.permission.CAMERA"
        )

    def find_available_apps(self, app_type: str) -> List[str]:
        """
        查找可用的特定类型应用

        Args:
            app_type: 应用类型 ('weather', 'camera', 'contacts', 'clock')

        Returns:
            List[str]: 可用应用包名列表
        """
        from .detectors.detector_utils import DetectorUtils

        # 定义不同类型应用的关键词
        app_keywords = {
            'weather': ['weather', 'clima', 'meteo'],
            'camera': ['camera', 'cam', 'photo'],
            'contacts': ['contacts', 'contact', 'people', 'dialer'],
            'clock': ['clock', 'time', 'alarm', 'timer', 'deskclock'],
            'music': ['music', 'player', 'visha'],
            'maps': ['maps', 'map', 'navigation'],
        }

        keywords = app_keywords.get(app_type, [])
        if not keywords:
            log.error(f"不支持的应用类型: {app_type}")
            return []

        return DetectorUtils.find_available_apps(keywords)

    def get_app_version(self, app_type: Union[AppType, str]) -> Optional[str]:
        """
        获取指定类型应用的版本信息

        Args:
            app_type: 应用类型

        Returns:
            Optional[str]: 应用版本，如果获取失败则返回None
        """
        from .detectors.detector_utils import DetectorUtils

        if isinstance(app_type, str):
            try:
                app_type = AppType(app_type)
            except ValueError:
                log.error(f"不支持的应用类型: {app_type}")
                return None

        detector = self.get_detector(app_type)
        if detector:
            packages = detector.get_package_names()
            for package in packages:
                version = DetectorUtils.get_app_version(package)
                if version:
                    return version

        return None

    def get_device_info(self) -> Dict[str, str]:
        """获取设备信息"""
        from .detectors.detector_utils import DetectorUtils
        return DetectorUtils.get_device_info()

    def check_app_in_foreground(self, app_type: Union[AppType, str]) -> bool:
        """
        检查指定类型的应用是否在前台

        Args:
            app_type: 应用类型

        Returns:
            bool: 应用是否在前台
        """
        from .detectors.detector_utils import DetectorUtils

        if isinstance(app_type, str):
            try:
                app_type = AppType(app_type)
            except ValueError:
                log.error(f"不支持的应用类型: {app_type}")
                return False

        detector = self.get_detector(app_type)
        if detector:
            packages = detector.get_package_names()
            for package in packages:
                if DetectorUtils.check_package_has_running_process(package):
                    if DetectorUtils.verify_app_in_foreground(package):
                        return True

        return False

    def get_running_apps_summary(self) -> Dict[str, bool]:
        """
        获取所有支持的应用类型的运行状态摘要

        Returns:
            Dict[str, bool]: 应用类型到运行状态的映射
        """
        summary = {}

        for app_type in AppType:
            try:
                summary[app_type.value] = self.check_app_opened(app_type)
            except Exception as e:
                log.debug(f"检查{app_type.value}应用状态失败: {e}")
                summary[app_type.value] = False

        return summary

    # ==================== 闹钟相关方法 ====================

    def check_alarm_status(self) -> bool:
        """检查闹钟状态"""
        from .detectors.alarm_detector import AlarmDetector
        alarm_detector = AlarmDetector()
        return alarm_detector.check_alarm_status()

    def get_alarm_list(self) -> List[Dict[str, any]]:
        """获取闹钟列表"""
        from .detectors.alarm_detector import AlarmDetector
        alarm_detector = AlarmDetector()
        return alarm_detector.get_alarm_list()

    def clear_all_alarms(self) -> bool:
        """清除所有闹钟"""
        from .detectors.alarm_detector import AlarmDetector
        alarm_detector = AlarmDetector()
        return alarm_detector.clear_all_alarms()

    def set_alarm(self, hour: int, minute: int, enabled: bool = True) -> bool:
        """设置闹钟"""
        from .detectors.alarm_detector import AlarmDetector
        alarm_detector = AlarmDetector()
        return alarm_detector.set_alarm(hour, minute, enabled)

    def get_next_alarm_info(self) -> Optional[Dict[str, any]]:
        """获取下一个闹钟信息"""
        from .detectors.alarm_detector import AlarmDetector
from app_detector import AppDetector, AppType
        alarm_detector = AlarmDetector()
        return alarm_detector.get_next_alarm_info()

    # ==================== 向后兼容的别名方法 ====================

    def check_clock_status(self) -> bool:
        """检查时钟应用状态 - 向后兼容"""
        return self.check_app_opened(AppType.CLOCK)

    def contacts_app_opened_alternative(self) -> bool:
        """联系人应用检测的替代方法 - 向后兼容"""
        return self.check_app_opened(AppType.CONTACTS)

    def check_contacts_app_opened_smart(self) -> bool:
        """智能检查联系人应用 - 向后兼容"""
        return self.check_app_opened(AppType.CONTACTS)

    def get_visha_package_name(self) -> str:
        """获取Visha包名 - 向后兼容"""
        music_detector = self.get_detector(AppType.MUSIC)
        if music_detector:
            packages = music_detector.get_package_names()
            visha_packages = [pkg for pkg in packages if 'visha' in pkg.lower()]
            if visha_packages:
                return visha_packages[0]
        return ""


# ==================== 测试和使用示例 ====================

if __name__ == '__main__':
    # 创建检测器实例
    detector = AppDetector()

    print("=" * 60)
    print("🔍 应用检测器 - 重构版本测试")
    print("=" * 60)

    # 测试新的API
    print("\n📱 检测各种应用状态:")

    # 使用新的统一API
    apps_to_check = [
        AppType.WEATHER,
        AppType.CAMERA,
        AppType.SETTINGS,
        AppType.CONTACTS,
        AppType.MUSIC,
        AppType.CLOCK
    ]

    for app_type in apps_to_check:
        try:
            status = detector.check_app_opened(app_type)
            print(f"  {app_type.value:12} : {'✅ 运行中' if status else '❌ 未运行'}")
        except Exception as e:
            print(f"  {app_type.value:12} : ⚠️ 检测失败 - {e}")

    # 测试运行状态摘要
    print(f"\n📊 运行状态摘要:")
    summary = detector.get_running_apps_summary()
    for app_type, status in summary.items():
        print(f"  {app_type:12} : {'✅' if status else '❌'}")

    # 测试设备信息
    print(f"\n📱 设备信息:")
    device_info = detector.get_device_info()
    for key, value in device_info.items():
        print(f"  {key:15} : {value}")

    # 测试闹钟功能
    print(f"\n⏰ 闹钟状态:")
    alarm_status = detector.check_alarm_status()
    print(f"  闹钟状态: {'✅ 有闹钟' if alarm_status else '❌ 无闹钟'}")

    alarms = detector.get_alarm_list()
    if alarms:
        print(f"  找到 {len(alarms)} 个闹钟:")
        for alarm in alarms[:3]:  # 只显示前3个
            print(f"    - {alarm.get('time', 'N/A')} ({'启用' if alarm.get('enabled', False) else '禁用'})")

    print("\n" + "=" * 60)
    print("✅ 测试完成")
    print("=" * 60)
