"""
应用检测器
负责检测各种应用的打开状态和权限状态
"""
import subprocess
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
sys.path.insert(0, str(project_root))

from core.logger import log


class AppDetector:
    """应用检测器"""

    def __init__(self):
        """独立应用检测器"""
        pass

    def check_weather_app_opened(self) -> bool:
        """
        检查是否有天气应用被打开
        
        Returns:
            bool: 是否有天气应用打开
        """
        try:
            log.info("检查天气应用状态")

            # 检查当前运行的应用
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                activity_output = result.stdout

                # 常见的天气应用包名
                weather_packages = [
                    "com.miui.weather",
                    "com.android.weather",
                    "com.google.android.apps.weather",
                    "com.transsion.weather",
                    "com.weather.forecast",
                    "com.accuweather",
                    "com.weather.channel",
                    "weather"
                ]

                for package in weather_packages:
                    if package in activity_output:
                        log.info(f"✅ 检测到天气应用: {package}")
                        return True

                log.info("未检测到专门的天气应用")
                return False
            else:
                log.error(f"获取应用活动失败: {result.stderr}")
                return False

        except Exception as e:
            log.error(f"检查天气应用失败: {e}")
            return False

    def check_camera_app_opened(self) -> bool:
        """
        检查是否有相机应用被打开
        
        Returns:
            bool: 是否有相机应用打开
        """
        try:
            log.info("检查相机应用状态")

            # 检查当前运行的应用
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                activity_output = result.stdout

                # 常见的相机应用包名
                camera_packages = [
                    "com.android.camera",
                    "com.google.android.GoogleCamera",
                    "com.transsion.camera",
                    "com.sec.android.app.camera",
                    "com.huawei.camera",
                    "com.xiaomi.camera",
                    "com.oppo.camera",
                    "com.vivo.camera",
                    "camera",
                    "Camera"
                ]

                for package in camera_packages:
                    if package in activity_output:
                        log.info(f"✅ 检测到相机应用: {package}")
                        return True

                log.info("未检测到专门的相机应用")
                return False
            else:
                log.error(f"获取应用活动失败: {result.stderr}")
                return False

        except Exception as e:
            log.error(f"检查相机应用失败: {e}")
            return False

    def check_settings_opened(self) -> bool:
        """
        检查是否有Settings应用被打开

        Returns:
            bool: 是否有Settings应用打开
        """
        try:
            log.info("检查Settings应用状态")

            # 方法1: 检查当前运行的应用活动
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=10
            )

            # 常见的Settings应用包名
            settings_packages = [
                "com.android.settings",  # 标准Android设置
                "com.transsion.settings",  # Transsion设置
                "com.sec.android.app.settings",  # 三星设置
                "com.huawei.android.settings",  # 华为设置
                "com.xiaomi.misettings",  # 小米设置
                "com.oppo.settings",  # OPPO设置
                "com.vivo.settings",  # Vivo设置
                "com.oneplus.settings",  # OnePlus设置
                "com.coloros.settings",  # ColorOS设置
                "com.miui.securitycenter",  # MIUI安全中心
            ]

            if result.returncode == 0:
                activity_output = result.stdout

                # 检查每个Settings包名
                for package in settings_packages:
                    if package in activity_output:
                        log.info(f"✅ 检测到Settings应用: {package}")
                        return True

                # 额外检查：使用更宽松的匹配
                settings_keywords = ["settings", "setting"]
                for keyword in settings_keywords:
                    if keyword in activity_output.lower():
                        # 进一步验证是否真的是Settings相关应用
                        lines = activity_output.split('\n')
                        for line in lines:
                            if keyword in line.lower() and ('activity' in line.lower() or 'task' in line.lower()):
                                # 排除一些非设置应用的误匹配
                                if not any(exclude in line.lower() for exclude in ['bluetooth', 'wifi', 'network']):
                                    log.info(f"✅ 通过关键词匹配检测到Settings应用: {keyword}")
                                    log.debug(f"匹配行: {line.strip()}")
                                    return True

                log.info("未检测到Settings应用")
                return False
            else:
                log.warning(f"方法1获取应用活动失败: {result.stderr}")

            # 方法2: 检查当前前台应用
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                window_output = result.stdout

                # 检查当前焦点窗口
                lines = window_output.split('\n')
                for line in lines:
                    if "mCurrentFocus" in line:
                        for package in settings_packages:
                            if package in line:
                                log.info(f"✅ Settings应用在前台: {package}")
                                log.debug(f"焦点窗口: {line.strip()}")
                                return True
                        break
            else:
                log.warning(f"方法2获取窗口信息失败: {result.stderr}")

            # 方法3: 检查当前活动的Activity
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "top"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                top_activity = result.stdout
                for package in settings_packages:
                    if package in top_activity and ("ACTIVITY" in top_activity or "RESUMED" in top_activity):
                        log.info(f"✅ Settings应用活动在顶部: {package}")
                        return True

            # 方法4: 检查运行中的进程
            result = subprocess.run(
                ["adb", "shell", "ps"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                ps_output = result.stdout

                # 检查主要Settings应用进程
                for package in settings_packages:
                    if package in ps_output:
                        log.info(f"✅ 通过进程检查检测到Settings应用: {package}")
                        return True

            log.info("所有检测方法均未发现活跃的Settings应用")
            return False

        except Exception as e:
            log.error(f"检查Settings应用失败: {e}")
            return False

    def check_contacts_app_opened(self) -> bool:
        """
        检查是否有联系人应用被打开 - 优化版本

        Returns:
            bool: 是否有联系人应用打开
        """
        try:
            log.info("检查联系人应用状态")

            # 联系人应用包名列表
            contacts_packages = [
                "com.sh.smart.caller",  # Dalier联系人应用
                "com.android.contacts",  # 标准联系人应用
                "com.google.android.contacts",  # Google联系人
                "com.samsung.android.app.contacts",  # 三星联系人
                "com.huawei.contacts",  # 华为联系人
                "com.xiaomi.contacts",  # 小米联系人
                "com.oppo.contacts",  # OPPO联系人
                "com.vivo.contacts",  # Vivo联系人
                "com.android.dialer",  # 标准拨号器
            ]

            # 方法1: 检查当前活动的Activity（最准确的方法）
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                activity_output = result.stdout
                log.debug(f"Activity输出前500字符: {activity_output[:500]}")

                # 检查topResumedActivity（最准确的前台应用指示器）
                lines = activity_output.split('\n')
                for line in lines:
                    if "topResumedActivity=" in line:
                        log.debug(f"找到topResumedActivity行: {line.strip()}")
                        for package in contacts_packages:
                            if package in line:
                                log.info(f"✅ 通过topResumedActivity检测到联系人应用: {package}")
                                return True
                        break

                # 检查mFocusedApp（备用方法）
                for line in lines:
                    if "mFocusedApp=" in line:
                        log.debug(f"找到mFocusedApp行: {line.strip()}")
                        for package in contacts_packages:
                            if package in line:
                                log.info(f"✅ 通过mFocusedApp检测到联系人应用: {package}")
                                return True
                        break

                # 检查RESUMED状态的Activity
                current_activity = ""
                for line in lines:
                    if "ActivityRecord{" in line and any(pkg in line for pkg in contacts_packages):
                        current_activity = line.strip()
                        log.debug(f"找到联系人应用Activity: {current_activity}")
                    elif current_activity and "state=RESUMED" in line:
                        log.info(f"✅ 检测到RESUMED状态的联系人应用")
                        return True
                    elif "ActivityRecord{" in line and not any(pkg in line for pkg in contacts_packages):
                        current_activity = ""  # 重置，因为这不是联系人应用

            # 方法2: 检查当前焦点窗口
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows"],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                window_output = result.stdout
                lines = window_output.split('\n')

                for line in lines:
                    if "mCurrentFocus=" in line:
                        log.debug(f"当前焦点窗口: {line.strip()}")
                        for package in contacts_packages:
                            if package in line:
                                log.info(f"✅ 通过焦点窗口检测到联系人应用: {package}")
                                return True
                        break

            # 方法3: 检查最近任务中的可见应用
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "recents"],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                recents_output = result.stdout
                for package in contacts_packages:
                    if package in recents_output and "visible=true" in recents_output:
                        log.info(f"✅ 通过最近任务检测到联系人应用: {package}")
                        return True

            log.info("未检测到活跃的联系人应用")
            return False

        except Exception as e:
            log.error(f"检查联系人应用失败: {e}")
            return False

    def _verify_app_in_foreground(self, package_name: str) -> bool:
        """
        验证应用是否真的在前台

        Args:
            package_name: 应用包名

        Returns:
            bool: 应用是否在前台
        """
        try:
            # 获取当前前台应用
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "recents"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                recents_output = result.stdout
                # 检查最近的任务中是否有该应用且处于前台
                if package_name in recents_output and "visible=true" in recents_output:
                    return True

            return False

        except Exception as e:
            log.debug(f"验证前台应用失败: {e}")
            return False

    def _verify_app_strictly_in_foreground(self, package_name: str) -> bool:
        """
        严格验证应用是否真的在前台显示

        Args:
            package_name: 应用包名

        Returns:
            bool: 应用是否严格在前台
        """
        try:
            log.debug(f"严格验证应用前台状态: {package_name}")

            # 方法1: 检查当前焦点窗口
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows"],
                capture_output=True,
                text=True,
                timeout=5,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                window_output = result.stdout
                lines = window_output.split('\n')

                for line in lines:
                    if "mCurrentFocus=" in line:
                        if package_name in line and "null" not in line:
                            log.debug(f"焦点窗口验证通过: {line.strip()}")
                            return True
                        break

            # 方法2: 检查应用的Activity状态
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "package", package_name],
                capture_output=True,
                text=True,
                timeout=5,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                package_output = result.stdout
                # 检查是否有RESUMED状态的Activity
                if "state=RESUMED" in package_output:
                    log.debug(f"Activity状态验证通过: 发现RESUMED状态")
                    return True

            # 方法3: 检查最近任务的可见性
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "recents"],
                capture_output=True,
                text=True,
                timeout=5,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                recents_output = result.stdout
                lines = recents_output.split('\n')

                in_target_task = False
                for line in lines:
                    if package_name in line and "Task{" in line:
                        in_target_task = True
                        log.debug(f"找到目标任务: {line.strip()}")
                    elif in_target_task and "visible=true" in line:
                        log.debug(f"任务可见性验证通过")
                        return True
                    elif in_target_task and "Task{" in line and package_name not in line:
                        in_target_task = False  # 进入其他任务

            log.debug(f"严格验证失败: {package_name} 不在前台")
            return False

        except Exception as e:
            log.debug(f"严格验证前台应用失败: {e}")
            return False

    def _check_package_has_running_process(self, package_name: str) -> bool:
        """
        严格检查指定包名是否有真正运行中的进程

        Args:
            package_name: 应用包名

        Returns:
            bool: 是否有真正运行中的进程
        """
        try:
            log.debug(f"严格检查包名运行进程: {package_name}")

            # 方法1: 使用ps命令直接搜索包名（最可靠）
            result = subprocess.run(
                ["adb", "shell", "ps"],
                capture_output=True,
                text=True,
                timeout=5,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                ps_output = result.stdout
                lines = ps_output.split('\n')

                for line in lines:
                    if package_name in line and line.strip():
                        # 确保这是一个真正的进程行，包含PID等信息
                        parts = line.split()
                        if len(parts) >= 8 and parts[-1] == package_name:
                            log.debug(f"通过ps找到真正的进程: {line.strip()}")
                            return True
                        elif package_name in line and any(status in line for status in ['S', 'R', 'D']):
                            log.debug(f"通过ps找到运行状态的进程: {line.strip()}")
                            return True

            # 方法2: 使用pidof命令（更直接）
            result = subprocess.run(
                ["adb", "shell", "pidof", package_name],
                capture_output=True,
                text=True,
                timeout=5,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0 and result.stdout.strip():
                pid = result.stdout.strip()
                log.debug(f"通过pidof找到进程ID: {pid}")

                # 进一步验证PID是否真实存在
                verify_result = subprocess.run(
                    ["adb", "shell", "ls", f"/proc/{pid}"],
                    capture_output=True,
                    text=True,
                    timeout=3,
                    encoding='utf-8',
                    errors='ignore'
                )

                if verify_result.returncode == 0:
                    log.debug(f"验证进程ID {pid} 确实存在")
                    return True

            log.debug(f"未找到包名 {package_name} 的真正运行进程")
            return False

        except Exception as e:
            log.debug(f"严格检查包名运行进程失败: {e}")
            return False

    def check_facebook_app_opened(self) -> bool:
        """
        检查是否有Facebook应用被打开

        Returns:
            bool: 是否有Facebook应用打开
        """
        try:
            log.info("检查Facebook应用状态")

            # 方法1: 检查当前运行的应用活动
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=10
            )

            # 常见的Facebook应用包名
            facebook_packages = [
                "com.facebook.katana",  # Facebook主应用
                "com.facebook.orca",  # Facebook Messenger
                "com.facebook.lite",  # Facebook Lite
                "com.facebook.mlite",  # Messenger Lite
                "com.facebook.pages.app",  # Facebook Pages Manager
                "com.facebook.work",  # Workplace from Facebook
            ]

            if result.returncode == 0:
                activity_output = result.stdout

                # 检查每个Facebook包名
                for package in facebook_packages:
                    if package in activity_output:
                        log.info(f"✅ 检测到Facebook应用: {package}")
                        return True

                # 额外检查：使用更宽松的匹配
                facebook_keywords = ["facebook", "fb"]
                for keyword in facebook_keywords:
                    if keyword in activity_output.lower():
                        # 进一步验证是否真的是Facebook相关应用
                        lines = activity_output.split('\n')
                        for line in lines:
                            if keyword in line.lower() and ('activity' in line.lower() or 'task' in line.lower()):
                                log.info(f"✅ 通过关键词匹配检测到Facebook应用: {keyword}")
                                log.debug(f"匹配行: {line.strip()}")
                                return True

                log.info("未检测到Facebook应用")
                return False
            else:
                log.warning(f"方法1获取应用活动失败: {result.stderr}")

            # 方法2: 检查当前前台应用
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                window_output = result.stdout

                # 检查当前焦点窗口
                lines = window_output.split('\n')
                for line in lines:
                    if "mCurrentFocus" in line:
                        for package in facebook_packages:
                            if package in line:
                                log.info(f"✅ Facebook应用在前台: {package}")
                                log.debug(f"焦点窗口: {line.strip()}")
                                return True
                        break
            else:
                log.warning(f"方法2获取窗口信息失败: {result.stderr}")

            # 方法2.5: 检查当前活动的Activity
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "top"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                top_activity = result.stdout
                for package in facebook_packages:
                    if package in top_activity and ("ACTIVITY" in top_activity or "RESUMED" in top_activity):
                        log.info(f"✅ Facebook应用活动在顶部: {package}")
                        return True

            # 方法3: 检查运行中的进程
            result = subprocess.run(
                ["adb", "shell", "ps"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                ps_output = result.stdout

                # 检查主要Facebook应用进程
                for package in facebook_packages:
                    if package in ps_output:
                        log.info(f"✅ 通过进程检查检测到Facebook应用: {package}")
                        return True

                # 检查Facebook相关服务（但需要进一步验证是否有前台应用）
                facebook_services = [
                    "com.facebook.services",
                    "com.facebook.system",
                ]

                for service in facebook_services:
                    if service in ps_output:
                        log.info(f"检测到Facebook服务: {service}")
                        # 如果只是服务在运行，需要进一步检查是否有实际的Facebook应用在前台
                        # 这里我们不直接返回True，而是继续检查

            # 方法4: 检查最近使用的应用
            try:
                result = subprocess.run(
                    ["adb", "shell", "dumpsys", "usagestats"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )

                if result.returncode == 0:
                    usage_output = result.stdout
                    for package in facebook_packages:
                        if package in usage_output:
                            # 进一步检查是否是最近活跃的
                            if "MOVE_TO_FOREGROUND" in usage_output or "ACTIVITY_RESUMED" in usage_output:
                                log.info(f"✅ 通过使用统计检测到活跃的Facebook应用: {package}")
                                return True
            except:
                pass

            log.info("所有检测方法均未发现活跃的Facebook应用")
            return False

        except Exception as e:
            log.error(f"检查Facebook应用失败: {e}")
            return False

    def check_contacts_app_opened_alternative(self) -> bool:
        """
        使用替代方法检查联系人应用是否打开 - 优化版本

        Returns:
            bool: 是否有联系人应用打开
        """
        try:
            log.info("使用替代方法检查联系人应用状态")

            contacts_packages = [
                "com.sh.smart.caller",  # Dalier联系人应用
                "com.android.contacts",  # 标准联系人应用
                "com.google.android.contacts",  # Google联系人
                "com.samsung.android.app.contacts",  # 三星联系人
                "com.huawei.contacts",  # 华为联系人
                "com.xiaomi.contacts",  # 小米联系人
                "com.oppo.contacts",  # OPPO联系人
                "com.vivo.contacts",  # Vivo联系人
                "com.android.dialer",  # 标准拨号器
            ]

            # 方法1: 使用dumpsys activity top获取当前顶部Activity
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "top"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                top_output = result.stdout
                log.debug(f"Top Activity输出前300字符: {top_output[:300]}")

                # 查找RESUMED状态的Activity
                lines = top_output.split('\n')
                for i, line in enumerate(lines):
                    if "RESUMED" in line:
                        # 检查前几行是否包含联系人应用包名
                        context_lines = lines[max(0, i - 5):i + 2]
                        context_text = '\n'.join(context_lines)

                        for package in contacts_packages:
                            if package in context_text:
                                log.info(f"✅ 通过Top Activity检测到RESUMED状态的联系人应用: {package}")
                                return True

            # 方法2: 检查进程状态
            result = subprocess.run(
                ["adb", "shell", "ps", "-A"],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                ps_output = result.stdout

                # 检查联系人应用进程是否存在
                for package in contacts_packages:
                    if package in ps_output:
                        log.debug(f"检测到联系人应用进程: {package}")

                        # 进一步验证该进程是否有前台Activity
                        if self._verify_process_has_foreground_activity(package):
                            log.info(f"✅ 通过进程检查检测到前台联系人应用: {package}")
                            return True

            # 方法3: 检查当前焦点窗口（简化版）
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows", "|", "grep", "-A", "1", "mCurrentFocus"],
                capture_output=True,
                text=True,
                timeout=5,
                shell=True
            )

            if result.returncode == 0 and result.stdout.strip():
                focus_output = result.stdout
                log.debug(f"焦点窗口信息: {focus_output}")

                for package in contacts_packages:
                    if package in focus_output:
                        log.info(f"✅ 通过焦点窗口检测到联系人应用: {package}")
                        return True

            log.info("所有替代方法都未检测到联系人应用")
            return False

        except Exception as e:
            log.error(f"替代方法检查联系人应用失败: {e}")
            return False

    def _verify_process_has_foreground_activity(self, package_name: str) -> bool:
        """
        验证进程是否有前台Activity

        Args:
            package_name: 应用包名

        Returns:
            bool: 进程是否有前台Activity
        """
        try:
            # 检查应用的Activity状态
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "package", package_name],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                package_output = result.stdout
                # 检查是否有RESUMED或visible的Activity
                if any(keyword in package_output for keyword in ["RESUMED", "visible=true", "mResumed=true"]):
                    return True

            return False

        except Exception as e:
            log.debug(f"验证进程前台Activity失败: {e}")
            return False

    def check_contacts_app_opened_smart(self) -> bool:
        """
        智能检查联系人应用是否打开 - 结合多种方法

        Returns:
            bool: 是否有联系人应用打开
        """
        try:
            log.info("使用智能方法检查联系人应用状态")

            # 首先尝试主要方法
            main_result = self.check_contacts_app_opened()
            if main_result:
                log.info("✅ 主要方法检测到联系人应用")
                return True

            # 如果主要方法失败，尝试替代方法
            log.info("主要方法未检测到，尝试替代方法")
            alternative_result = self.check_contacts_app_opened_alternative()
            if alternative_result:
                log.info("✅ 替代方法检测到联系人应用")
                return True

            # 最后尝试简单的包名检查
            log.info("尝试简单包名检查")
            simple_result = self._simple_package_check()
            if simple_result:
                log.info("✅ 简单包名检查检测到联系人应用")
                return True

            log.info("所有方法都未检测到联系人应用")
            return False

        except Exception as e:
            log.error(f"智能检查联系人应用失败: {e}")
            return False

    def _simple_package_check(self) -> bool:
        """
        简单的包名检查方法

        Returns:
            bool: 是否检测到联系人应用
        """
        try:
            # 检查当前运行的应用包
            result = subprocess.run(
                ["adb", "shell", "pm", "list", "packages", "-e"],
                capture_output=True,
                text=True,
                timeout=5,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                packages_output = result.stdout
                contacts_packages = ["com.sh.smart.caller", "com.android.contacts"]

                for package in contacts_packages:
                    if package in packages_output:
                        # 进一步检查该包是否有活动进程
                        ps_result = subprocess.run(
                            ["adb", "shell", "ps", "|", "grep", package],
                            capture_output=True,
                            text=True,
                            timeout=3,
                            shell=True,
                            encoding='utf-8',
                            errors='ignore'
                        )

                        if ps_result.returncode == 0 and ps_result.stdout.strip():
                            log.debug(f"检测到联系人应用进程: {package}")
                            return True

            return False

        except Exception as e:
            log.debug(f"简单包名检查失败: {e}")
            return False

    def check_camera_permission(self) -> bool:
        """
        检查相机权限状态
        
        Returns:
            bool: 是否有相机权限
        """
        try:
            log.info("检查相机权限状态")

            # 检查Ella应用的相机权限
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "package", "com.transsion.aivoiceassistant"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                package_output = result.stdout

                # 查找相机权限相关信息
                camera_permission_indicators = [
                    "android.permission.CAMERA",
                    "CAMERA: granted=true",
                    "camera permission"
                ]

                for indicator in camera_permission_indicators:
                    if indicator in package_output:
                        log.info(f"✅ 检测到相机权限信息: {indicator}")
                        return True

                log.info("未检测到相机权限信息")
                return False
            else:
                log.error(f"获取权限信息失败: {result.stderr}")
                return False

        except Exception as e:
            log.error(f"检查相机权限失败: {e}")
            return False

    def check_clock_app_opened(self, clock_package_name: str = "com.transsion.deskclock") -> bool:
        """
        检查时钟应用是否打开
        
        Args:
            clock_package_name: 时钟应用包名
            
        Returns:
            bool: 时钟应用是否正在运行
        """
        try:
            log.info("检查时钟应用运行状态")

            # 方法1: 检查当前运行的应用
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                output = result.stdout
                if clock_package_name in output:
                    log.info(f"✅ 时钟应用正在运行: {clock_package_name}")
                    return True

            # 方法2: 检查当前焦点窗口
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                output = result.stdout
                if clock_package_name in output and "mCurrentFocus" in output:
                    log.info(f"✅ 时钟应用在前台: {clock_package_name}")
                    return True

            # 方法3: 检查进程
            result = subprocess.run(
                ["adb", "shell", "ps", "|", "grep", clock_package_name],
                capture_output=True,
                text=True,
                timeout=5,
                shell=True
            )

            if result.returncode == 0 and result.stdout.strip():
                log.info(f"✅ 时钟应用进程存在: {clock_package_name}")
                return True

            log.info(f"❌ 时钟应用未运行: {clock_package_name}")
            return False

        except Exception as e:
            log.error(f"检查时钟应用状态失败: {e}")
            return False

    def check_google_map_app_opened(self) -> bool:
        """
        检查Google地图应用是否打开

        Returns:
            bool: Google地图应用是否正在运行
        """
        try:
            log.info("检查Google地图应用运行状态")

            # 方法1: 检查当前运行的应用
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                output = result.stdout
                if "com.google.android.apps.maps" in output:
                    log.info("✅ Google地图应用正在运行")
                    return True

            # 方法2: 检查当前焦点窗口
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                output = result.stdout
                if "com.google.android.apps.maps" in output and "mCurrentFocus" in output:
                    log.info("✅ Google地图应用在前台")
                    return True

            # 方法3: 检查进程
            result = subprocess.run(
                ["adb", "shell", "ps", "|", "grep", "com.google.android.apps.maps"],
                capture_output=True,
                text=True,
                timeout=5,
                shell=True
            )

            if result.returncode == 0 and result.stdout.strip():
                log.info("✅ Google地图应用进程存在")
                return True

            log.info("❌ Google地图应用未运行")
            return False

        except Exception as e:
            log.error(f"检查Google地图应用状态失败: {e}")
            return False
    
    def check_google_playstore_app_opened(self) -> bool:
        """
        检查Google Play Store应用是否启动 - 精确的vending进程检测

        Returns:
            bool: Google Play Store应用进程是否正在运行
        """
        try:
            log.info("检查Google Play Store应用进程运行状态")

            # 方法1: 优先检查vending主进程及其子进程（最准确的指标）
            result = subprocess.run(
                ["adb", "shell", "ps"],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                ps_output = result.stdout
                log.debug(f"进程列表前500字符: {ps_output[:500]}")

                # 严格检查vending相关进程（必须是真正的进程行）
                vending_patterns = [
                    "com.android.vending",  # 主进程
                    "com.android.vending:background",  # 后台服务
                    "com.android.vending:quick_launch",  # 快速启动服务
                ]

                lines = ps_output.split('\n')
                for line in lines:
                    line = line.strip()
                    if not line:
                        continue

                    for pattern in vending_patterns:
                        if pattern in line:
                            # 验证这是一个真正的进程行（包含PID、状态等）
                            parts = line.split()
                            if len(parts) >= 8:  # 标准ps输出应该有至少8个字段
                                # 检查是否有进程状态（S, R, D等）
                                if any(status in parts for status in ['S', 'R', 'D', 'T', 'Z']):
                                    log.info(f"✅ 检测到Google Play Store进程: {pattern}")
                                    log.debug(f"进程行: {line}")
                                    return True
                            else:
                                log.debug(f"跳过非标准进程行: {line}")

                log.debug("未找到真正的vending进程，继续检查其他方法")
            else:
                log.warning(f"方法1获取进程列表失败: {result.stderr}")

            # 方法2: 使用ps -A命令检查vending进程（更全面）
            result = subprocess.run(
                ["adb", "shell", "ps", "-A"],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                ps_a_output = result.stdout

                # 只检查vending相关进程
                if "com.android.vending" in ps_a_output:
                    log.info(f"✅ 通过ps -A检测到Google Play Store进程: com.android.vending")
                    return True

                log.debug("ps -A中未找到vending进程")
            else:
                log.warning(f"方法2获取详细进程列表失败: {result.stderr}")

            # 方法3: 使用dumpsys activity processes检查vending进程（排除DEAD连接）
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "processes"],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                processes_output = result.stdout

                # 检查vending进程，但排除DEAD连接
                if "com.android.vending" in processes_output:
                    lines = processes_output.split('\n')
                    for line in lines:
                        if "com.android.vending" in line:
                            # 排除DEAD连接和其他非活跃状态
                            if "DEAD" not in line and "ConnectionRecord" not in line:
                                log.info(f"✅ 通过activity processes检测到活跃的Google Play Store进程")
                                log.debug(f"活跃进程行: {line.strip()}")
                                return True
                            else:
                                log.debug(f"跳过非活跃连接: {line.strip()}")

                log.debug("activity processes中未找到活跃的vending进程")
            else:
                log.warning(f"方法3获取活动进程失败: {result.stderr}")

            # 方法4: 直接检查vending包的运行进程
            if self._check_package_has_running_process("com.android.vending"):
                log.info(f"✅ 验证Google Play Store包有运行进程: com.android.vending")
                return True

            log.info("所有检测方法均未发现运行中的Google Play Store进程")
            return False

        except Exception as e:
            log.error(f"检查Google Play Store应用进程失败: {e}")
            return False

    def check_visha_app_opened(self, include_background: bool = True) -> bool:
        """
        检查Visha音乐应用是否打开 - 优化版本

        Args:
            include_background: 是否包括后台运行的应用 (默认True)

        Returns:
            bool: Visha音乐应用是否正在运行
        """
        try:
            log.info("检查Visha音乐应用运行状态")

            # 定义可能的Visha音乐应用包名
            visha_packages = [
                "com.visha.music",           # Visha音乐主应用
                "com.transsion.magicshow",   # Transsion Magic Show (可能的别名)
                "com.transsion.visha",       # Transsion Visha
                "com.visha.player",          # Visha播放器
                "com.visha",                 # Visha通用包名
            ]

            # 方法1: 检查当前活动的应用 (最准确)
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                activity_output = result.stdout
                log.debug(f"Activity输出前500字符: {activity_output[:500]}")

                # 检查topResumedActivity（最准确的前台应用指示器）
                lines = activity_output.split('\n')
                for line in lines:
                    if "topResumedActivity=" in line:
                        log.debug(f"找到topResumedActivity行: {line.strip()}")
                        for package in visha_packages:
                            if package in line:
                                log.info(f"✅ 通过topResumedActivity检测到Visha应用: {package}")
                                return True
                        break

                # 检查mFocusedApp（备用前台检测方法）
                for line in lines:
                    if "mFocusedApp=" in line:
                        log.debug(f"找到mFocusedApp行: {line.strip()}")
                        for package in visha_packages:
                            if package in line and "null" not in line:
                                log.info(f"✅ 通过mFocusedApp检测到前台Visha应用: {package}")
                                return True
                        break

                # 检查RESUMED状态的Activity
                for package in visha_packages:
                    if package in activity_output:
                        # 进一步检查是否处于RESUMED状态
                        package_lines = [line for line in lines if package in line]
                        for package_line in package_lines:
                            if "RESUMED" in activity_output[activity_output.find(package_line):activity_output.find(package_line) + 200]:
                                log.info(f"✅ 检测到RESUMED状态的Visha应用: {package}")
                                return True

            # 方法2: 检查当前焦点窗口
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows"],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                window_output = result.stdout
                lines = window_output.split('\n')

                for line in lines:
                    if "mCurrentFocus=" in line:
                        log.debug(f"当前焦点窗口: {line.strip()}")
                        for package in visha_packages:
                            if package in line and "null" not in line:
                                log.info(f"✅ 通过焦点窗口检测到Visha应用: {package}")
                                return True
                        break

            # 方法3: 检查运行进程 (使用更可靠的方法)
            result = subprocess.run(
                ["adb", "shell", "ps"],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                ps_output = result.stdout
                lines = ps_output.split('\n')

                for line in lines:
                    line = line.strip()
                    if not line:
                        continue

                    for package in visha_packages:
                        if package in line:
                            # 验证这是一个真正的进程行（包含PID、状态等）
                            parts = line.split()
                            if len(parts) >= 8:  # 标准ps输出应该有至少8个字段
                                # 检查是否有进程状态（S, R, D等）
                                if any(status in parts for status in ['S', 'R', 'D', 'T']):
                                    log.info(f"✅ 检测到Visha应用进程: {package}")
                                    log.debug(f"进程行: {line}")

                                    # 如果包括后台应用，直接返回True
                                    if include_background:
                                        log.info(f"✅ Visha应用正在运行（包括后台）: {package}")
                                        return True

                                    # 如果只检查前台应用，使用简单的前台检测
                                    if self._simple_foreground_check(package):
                                        log.info(f"✅ 验证Visha应用在前台: {package}")
                                        return True
                                    else:
                                        log.debug(f"Visha应用进程存在但不在前台: {package}")

            # 方法4: 使用严格的包名检查
            for package in visha_packages:
                if self._check_package_has_running_process(package):
                    log.info(f"✅ 验证Visha应用包有运行进程: {package}")

                    # 如果包括后台应用，直接返回True
                    if include_background:
                        log.info(f"✅ 最终确认Visha应用正在运行（包括后台）: {package}")
                        return True

                    # 如果只检查前台应用，使用简单的前台检测
                    if self._simple_foreground_check(package):
                        log.info(f"✅ 最终确认Visha应用在前台: {package}")
                        return True

            # 方法5: 检查最近任务中的可见应用
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "recents"],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                recents_output = result.stdout
                for package in visha_packages:
                    if package in recents_output and "visible=true" in recents_output:
                        log.info(f"✅ 通过最近任务检测到可见的Visha应用: {package}")
                        return True

            log.info("❌ 所有检测方法均未发现运行中的Visha音乐应用")
            return False

        except Exception as e:
            log.error(f"检查Visha音乐应用状态失败: {e}")
            return False

    def check_visha_app_in_foreground(self) -> bool:
        """
        检查Visha音乐应用是否在前台运行

        Returns:
            bool: Visha音乐应用是否在前台
        """
        return self.check_visha_app_opened(include_background=False)

    def check_visha_app_running(self) -> bool:
        """
        检查Visha音乐应用是否正在运行（包括后台）

        Returns:
            bool: Visha音乐应用是否正在运行
        """
        return self.check_visha_app_opened(include_background=True)

    def _simple_foreground_check(self, package_name: str) -> bool:
        """
        简单的前台检测方法 - 更宽松的检测逻辑

        Args:
            package_name: 应用包名

        Returns:
            bool: 应用是否在前台
        """
        try:
            log.debug(f"简单前台检测: {package_name}")

            # 方法1: 检查当前焦点窗口
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows"],
                capture_output=True,
                text=True,
                timeout=5,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                window_output = result.stdout
                lines = window_output.split('\n')

                for line in lines:
                    if "mCurrentFocus=" in line:
                        if package_name in line and "null" not in line:
                            log.debug(f"✅ 简单前台检测通过 - 焦点窗口: {line.strip()}")
                            return True
                        break

            # 方法2: 检查topResumedActivity
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=5,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                activity_output = result.stdout

                # 检查topResumedActivity
                if "topResumedActivity=" in activity_output and package_name in activity_output:
                    lines = activity_output.split('\n')
                    for line in lines:
                        if "topResumedActivity=" in line and package_name in line:
                            log.debug(f"✅ 简单前台检测通过 - topResumedActivity: {line.strip()}")
                            return True

                # 检查mFocusedApp
                if "mFocusedApp=" in activity_output and package_name in activity_output:
                    lines = activity_output.split('\n')
                    for line in lines:
                        if "mFocusedApp=" in line and package_name in line and "null" not in line:
                            log.debug(f"✅ 简单前台检测通过 - mFocusedApp: {line.strip()}")
                            return True

            log.debug(f"简单前台检测失败: {package_name}")
            return False

        except Exception as e:
            log.debug(f"简单前台检测异常: {e}")
            return False

    def check_music_app_opened(self) -> bool:
        """
        检查音乐应用是否打开 - 通用音乐应用检测方法

        Returns:
            bool: 是否有音乐应用正在运行
        """
        try:
            log.info("检查音乐应用运行状态")

            # 常见的音乐应用包名
            music_packages = [
                "com.visha.music",           # Visha音乐
                "com.transsion.magicshow",   # Transsion Magic Show
                "com.android.music",         # 系统音乐
                "com.google.android.music",  # Google Play Music
                "com.spotify.music",         # Spotify
                "com.netease.cloudmusic",    # 网易云音乐
                "com.tencent.qqmusic",       # QQ音乐
                "com.kugou.android",         # 酷狗音乐
                "com.kuwo.kwmusic",          # 酷我音乐
                "com.miui.player",           # 小米音乐
                "com.samsung.android.music", # 三星音乐
                "com.huawei.music",          # 华为音乐
            ]

            # 使用现有的检测逻辑
            for package in music_packages:
                if self._check_package_has_running_process(package):
                    if self._verify_app_strictly_in_foreground(package):
                        log.info(f"✅ 检测到运行中的音乐应用: {package}")
                        return True

            log.info("❌ 未检测到运行中的音乐应用")
            return False

        except Exception as e:
            log.error(f"检查音乐应用状态失败: {e}")
            return False

    def get_visha_package_name(self) -> str:
        """
        获取设备上实际安装的Visha音乐应用包名

        Returns:
            str: 实际的Visha包名，如果未找到则返回空字符串
        """
        try:
            log.info("查找设备上的Visha音乐应用包名")

            # 可能的Visha包名
            visha_candidates = [
                "com.visha.music",
                "com.transsion.magicshow",
                "com.transsion.visha",
                "com.visha.player",
                "com.visha"
            ]

            # 获取所有已安装的应用包
            result = subprocess.run(
                ["adb", "shell", "pm", "list", "packages"],
                capture_output=True,
                text=True,
                timeout=15
            )

            if result.returncode == 0:
                installed_packages = result.stdout

                # 检查哪个Visha包名实际安装了
                for package in visha_candidates:
                    if f"package:{package}" in installed_packages:
                        log.info(f"✅ 找到已安装的Visha应用: {package}")
                        return package

                # 如果没有找到精确匹配，尝试模糊匹配
                lines = installed_packages.split('\n')
                for line in lines:
                    if 'visha' in line.lower() or 'magic' in line.lower():
                        package_name = line.replace('package:', '').strip()
                        if package_name:
                            log.info(f"✅ 通过模糊匹配找到可能的Visha应用: {package_name}")
                            return package_name

            log.info("❌ 未找到Visha音乐应用")
            return ""

        except Exception as e:
            log.error(f"查找Visha包名失败: {e}")
            return ""

    def find_available_apps(self, app_type: str) -> list:
        """
        查找可用的特定类型应用

        Args:
            app_type: 应用类型 ('weather', 'camera', 'contacts', 'clock')

        Returns:
            list: 可用应用包名列表
        """
        try:
            log.info(f"查找可用的{app_type}应用")

            # 获取所有已安装的应用包
            result = subprocess.run(
                ["adb", "shell", "pm", "list", "packages"],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                log.error(f"获取应用包列表失败: {result.stderr}")
                return []

            packages = result.stdout.strip().split('\n')
            found_apps = []

            # 定义不同类型应用的关键词
            app_keywords = {
                'weather': ['weather', 'clima', 'meteo'],
                'camera': ['camera', 'cam', 'photo'],
                'contacts': ['contacts', 'contact', 'people', 'dialer'],
                'clock': ['clock', 'time', 'alarm', 'timer', 'deskclock']
            }

            keywords = app_keywords.get(app_type, [])

            for package_line in packages:
                if package_line.startswith('package:'):
                    package_name = package_line.replace('package:', '')

                    # 检查是否包含相关关键词
                    for keyword in keywords:
                        if keyword.lower() in package_name.lower():
                            found_apps.append(package_name)
                            break

            log.info(f"找到{len(found_apps)}个{app_type}应用: {found_apps}")
            return found_apps

        except Exception as e:
            log.error(f"查找{app_type}应用失败: {e}")
            return []

    # ==================== Clock & Alarm 相关方法 ====================

    def check_clock_status(self) -> bool:
        """
        检查时钟应用状态

        Returns:
            bool: 时钟应用是否正在运行
        """
        return self.check_clock_app_opened()

    def check_alarm_status(self) -> bool:
        """
        检查闹钟状态 - 通过ADB命令获取闹钟列表

        Returns:
            bool: 是否有设置的闹钟
        """
        try:
            log.info("检查闹钟状态")

            # 通过ADB命令检查闹钟设置
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "alarm"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                alarm_output = result.stdout
                # 检查是否包含闹钟相关信息
                alarm_indicators = [
                    "com.transsion.deskclock",
                    "AlarmManager",
                    "alarm_clock",
                    "RTC_WAKEUP"
                ]

                for indicator in alarm_indicators:
                    if indicator in alarm_output:
                        log.info(f"检测到闹钟相关信息: {indicator}")
                        return True

                log.info("未检测到活跃的闹钟")
                return False
            else:
                log.error(f"获取闹钟状态失败: {result.stderr}")
                return False

        except Exception as e:
            log.error(f"检查闹钟状态失败: {e}")
            return False

    def get_alarm_list(self) -> list:
        """
        获取闹钟列表

        Returns:
            list: 闹钟列表
        """
        try:
            log.info("获取闹钟列表")
            alarms = []

            # 方法1: 通过dumpsys alarm获取
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "alarm"],
                capture_output=True,
                text=True,
                timeout=15
            )

            if result.returncode == 0:
                alarm_output = result.stdout
                # 解析闹钟信息
                alarms.extend(self._parse_alarm_database_output(alarm_output))

            log.info(f"找到 {len(alarms)} 个闹钟")
            return alarms

        except Exception as e:
            log.error(f"获取闹钟列表失败: {e}")
            return []

    def _parse_alarm_database_output(self, output: str) -> list:
        """
        解析闹钟数据库输出

        Args:
            output: 数据库输出内容

        Returns:
            list: 解析出的闹钟列表
        """
        alarms = []
        try:
            lines = output.split('\n')
            for line in lines:
                line = line.strip()
                # 查找包含时间信息的行
                if any(keyword in line.lower() for keyword in ['alarm', 'clock', 'time']):
                    # 简单的时间模式匹配
                    import re
                    time_pattern = r'\b([0-1]?[0-9]|2[0-3]):[0-5][0-9]\b'
                    matches = re.findall(time_pattern, line)
                    for match in matches:
                        if match not in [alarm.get('time') for alarm in alarms]:
                            alarms.append({
                                'time': match,
                                'enabled': 'enabled' in line.lower() or 'on' in line.lower(),
                                'source': 'dumpsys'
                            })
        except Exception as e:
            log.debug(f"解析闹钟输出失败: {e}")

        return alarms

    def clear_all_alarms(self) -> bool:
        """
        清除所有闹钟

        Returns:
            bool: 是否成功清除
        """
        try:
            log.info("清除所有闹钟")

            # 通过ADB命令清除闹钟
            commands = [
                ["adb", "shell", "am", "broadcast", "-a", "android.intent.action.ALARM_CHANGED"],
                ["adb", "shell", "settings", "put", "system", "alarm_alert", ""],
            ]

            success_count = 0
            for cmd in commands:
                try:
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        success_count += 1
                except Exception as e:
                    log.debug(f"执行清除命令失败: {e}")

            success = success_count > 0
            log.info(f"闹钟清除{'成功' if success else '失败'}")
            return success

        except Exception as e:
            log.error(f"清除闹钟失败: {e}")
            return False


if __name__ == '__main__':
    detector = AppDetector()

    print("=" * 60)
    print("🎵 Visha音乐应用检测测试 - 优化版本")
    print("=" * 60)

    # # 1. 检查Visha应用是否运行（包括后台）
    # running_status = detector.check_visha_app_running()
    # print(f"Visha应用运行状态（包括后台）: {running_status}")
    #
    # # 2. 检查Visha应用是否在前台
    # foreground_status = detector.check_visha_app_in_foreground()
    # print(f"Visha应用前台状态: {foreground_status}")
    #
    # # 3. 获取实际的Visha包名
    # visha_package = detector.get_visha_package_name()
    # print(f"检测到的Visha包名: {visha_package if visha_package else '未找到'}")

    # 4. 检查通用音乐应用
    music_status = detector.check_visha_app_opened()
    print(f"音乐应用运行状态: {music_status}")

