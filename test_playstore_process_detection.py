#!/usr/bin/env python3
"""
测试Google Play Store进程检测功能
"""
import sys
import os
import subprocess
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pages.base.app_detector import AppDetector
from core.logger import log


def get_running_processes():
    """获取当前运行的进程列表"""
    try:
        result = subprocess.run(
            ["adb", "shell", "ps"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            return result.stdout
        return "获取失败"
    except Exception as e:
        return f"获取失败: {e}"


def check_playstore_processes_manually():
    """手动检查Play Store相关进程"""
    print("\n🔍 手动检查Play Store相关进程...")
    
    playstore_packages = [
        "com.android.vending",
        "com.google.android.finsky", 
        "com.android.vending.billing",
        "com.google.android.gms.policy_sidecar_aps",
        "com.google.android.packageinstaller"
    ]
    
    try:
        # 获取所有进程
        result = subprocess.run(
            ["adb", "shell", "ps"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            ps_output = result.stdout
            found_processes = []
            
            for package in playstore_packages:
                if package in ps_output:
                    found_processes.append(package)
                    print(f"   ✅ 找到进程: {package}")
            
            if not found_processes:
                print("   ❌ 未找到任何Play Store相关进程")
            
            return len(found_processes) > 0
        else:
            print("   ❌ 无法获取进程列表")
            return False
            
    except Exception as e:
        print(f"   ❌ 手动检查失败: {e}")
        return False


def test_playstore_process_detection():
    """测试Google Play Store进程检测功能"""
    print("=" * 70)
    print("测试Google Play Store应用进程检测功能")
    print("=" * 70)
    
    detector = AppDetector()
    
    try:
        # 显示当前进程概况
        print("\n📊 当前系统进程概况:")
        ps_output = get_running_processes()
        if "获取失败" not in ps_output:
            lines = ps_output.split('\n')
            print(f"   总进程数: {len([line for line in lines if line.strip()])}")
        else:
            print(f"   {ps_output}")
        
        # 手动检查Play Store进程
        manual_result = check_playstore_processes_manually()
        
        # 使用优化后的方法检测
        print("\n🔍 使用优化后的方法检测Play Store进程...")
        result = detector.check_google_playstore_app_opened()
        
        if result:
            print("✅ 检测结果: Google Play Store进程正在运行")
        else:
            print("❌ 检测结果: Google Play Store进程未运行")
            
        print(f"\n📊 最终检测结果: {result}")
        print(f"📊 手动检查结果: {manual_result}")
        
        # 结果对比
        if result == manual_result:
            print("✅ 检测结果与手动检查一致")
        else:
            print("⚠️  检测结果与手动检查不一致，需要进一步调试")
        
        # 详细进程信息
        print("\n🔍 详细进程检查...")
        try:
            # 使用ps -A获取更详细的进程信息
            result_detailed = subprocess.run(
                ["adb", "shell", "ps", "-A"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result_detailed.returncode == 0:
                ps_a_output = result_detailed.stdout
                playstore_lines = []
                
                for line in ps_a_output.split('\n'):
                    if any(pkg in line for pkg in ["vending", "finsky", "playstore"]):
                        playstore_lines.append(line.strip())
                
                if playstore_lines:
                    print("   找到的Play Store相关进程:")
                    for line in playstore_lines:
                        print(f"   📋 {line}")
                else:
                    print("   ❌ 未找到Play Store相关进程")
            else:
                print("   ❌ 无法获取详细进程信息")
                
        except Exception as e:
            print(f"   ❌ 详细检查失败: {e}")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        log.error(f"测试失败: {e}")
        return False
    
    print("\n" + "=" * 70)
    print("测试完成")
    print("=" * 70)
    return True


def test_process_detection_scenarios():
    """测试不同场景下的进程检测"""
    print("\n" + "=" * 70)
    print("测试不同场景下的进程检测")
    print("=" * 70)
    
    detector = AppDetector()
    
    scenarios = [
        ("当前状态", None),
        ("启动Play Store后", "start_playstore"),
        ("返回桌面后", "go_home"),
        ("强制停止后", "force_stop")
    ]
    
    for scenario_name, action in scenarios:
        print(f"\n📊 场景: {scenario_name}")
        
        # 执行操作
        if action == "start_playstore":
            try:
                subprocess.run(
                    ["adb", "shell", "am", "start", "-n", "com.android.vending/.AssetBrowserActivity"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                print("   已发送启动命令")
                time.sleep(3)
            except Exception as e:
                print(f"   启动失败: {e}")
                
        elif action == "go_home":
            try:
                subprocess.run(
                    ["adb", "shell", "input", "keyevent", "KEYCODE_HOME"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                print("   已返回桌面")
                time.sleep(2)
            except Exception as e:
                print(f"   返回桌面失败: {e}")
                
        elif action == "force_stop":
            try:
                subprocess.run(
                    ["adb", "shell", "am", "force-stop", "com.android.vending"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                print("   已强制停止Play Store")
                time.sleep(2)
            except Exception as e:
                print(f"   强制停止失败: {e}")
        
        # 检测结果
        result = detector.check_google_playstore_app_opened()
        print(f"   检测结果: {result}")


if __name__ == "__main__":
    test_playstore_process_detection()
    test_process_detection_scenarios()
