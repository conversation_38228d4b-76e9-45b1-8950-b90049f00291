#!/usr/bin/env python3
"""
分析当前设备上的Google Play Store相关包名和进程
"""
import sys
import os
import subprocess
import re

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.logger import log


def get_all_packages():
    """获取所有已安装的包"""
    try:
        result = subprocess.run(
            ["adb", "shell", "pm", "list", "packages"],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            packages = []
            for line in result.stdout.strip().split('\n'):
                if line.startswith('package:'):
                    packages.append(line.replace('package:', ''))
            return packages
        return []
    except Exception as e:
        print(f"获取包列表失败: {e}")
        return []


def find_playstore_related_packages():
    """查找Play Store相关的包"""
    print("🔍 查找Play Store相关包名...")
    
    all_packages = get_all_packages()
    
    # 定义可能的Play Store相关关键词
    keywords = [
        'vending', 'play', 'store', 'finsky', 'market', 
        'google.android.gms', 'google.android.gsf'
    ]
    
    playstore_packages = []
    
    for package in all_packages:
        for keyword in keywords:
            if keyword in package.lower():
                playstore_packages.append(package)
                break
    
    print(f"找到 {len(playstore_packages)} 个相关包:")
    for package in sorted(playstore_packages):
        print(f"  📦 {package}")
    
    return playstore_packages


def check_running_processes():
    """检查当前运行的进程"""
    print("\n🔍 检查当前运行的相关进程...")
    
    try:
        result = subprocess.run(
            ["adb", "shell", "ps"],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            
            # 查找相关进程
            keywords = ['vending', 'play', 'store', 'finsky', 'gms', 'gsf']
            running_processes = []
            
            for line in lines:
                for keyword in keywords:
                    if keyword in line.lower():
                        running_processes.append(line.strip())
                        break
            
            if running_processes:
                print(f"找到 {len(running_processes)} 个相关进程:")
                for process in running_processes:
                    print(f"  🔄 {process}")
            else:
                print("  ❌ 未找到相关进程")
            
            return running_processes
        else:
            print("  ❌ 获取进程列表失败")
            return []
            
    except Exception as e:
        print(f"  ❌ 检查进程失败: {e}")
        return []


def get_package_info(package_name):
    """获取包的详细信息"""
    try:
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "package", package_name],
            capture_output=True,
            text=True,
            timeout=15
        )
        
        if result.returncode == 0:
            output = result.stdout
            
            # 提取关键信息
            info = {}
            
            # 查找版本信息
            version_match = re.search(r'versionName=([^\s]+)', output)
            if version_match:
                info['version'] = version_match.group(1)
            
            # 查找是否启用
            enabled_match = re.search(r'enabled=(\w+)', output)
            if enabled_match:
                info['enabled'] = enabled_match.group(1)
            
            # 查找安装位置
            install_match = re.search(r'codePath=([^\s]+)', output)
            if install_match:
                info['install_path'] = install_match.group(1)
            
            return info
        return {}
    except Exception as e:
        return {'error': str(e)}


def analyze_main_playstore_candidates():
    """分析主要的Play Store候选包"""
    print("\n🔍 分析主要Play Store候选包...")
    
    candidates = [
        'com.android.vending',
        'com.google.android.finsky', 
        'com.google.android.gms',
        'com.google.android.gsf'
    ]
    
    for package in candidates:
        print(f"\n📦 分析包: {package}")
        
        # 检查包是否存在
        try:
            result = subprocess.run(
                ["adb", "shell", "pm", "path", package],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0 and result.stdout.strip():
                print(f"  ✅ 包已安装: {result.stdout.strip()}")
                
                # 获取详细信息
                info = get_package_info(package)
                if info:
                    for key, value in info.items():
                        print(f"    {key}: {value}")
                
                # 检查是否有运行进程
                ps_result = subprocess.run(
                    ["adb", "shell", "ps", "|", "grep", package],
                    capture_output=True,
                    text=True,
                    timeout=5,
                    shell=True
                )
                
                if ps_result.returncode == 0 and ps_result.stdout.strip():
                    print(f"  🔄 进程运行中:")
                    for line in ps_result.stdout.strip().split('\n'):
                        print(f"    {line}")
                else:
                    print(f"  ❌ 无运行进程")
                    
            else:
                print(f"  ❌ 包未安装")
                
        except Exception as e:
            print(f"  ❌ 检查失败: {e}")


def test_current_detection_method():
    """测试当前的检测方法"""
    print("\n🔍 测试当前检测方法...")
    
    try:
        from pages.base.app_detector import AppDetector
        detector = AppDetector()
        
        result = detector.check_google_playstore_app_opened()
        print(f"  当前检测结果: {result}")
        
        return result
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 70)
    print("Google Play Store 包名和进程分析")
    print("=" * 70)
    
    # 1. 查找相关包
    playstore_packages = find_playstore_related_packages()
    
    # 2. 检查运行进程
    running_processes = check_running_processes()
    
    # 3. 分析主要候选包
    analyze_main_playstore_candidates()
    
    # 4. 测试当前检测方法
    current_result = test_current_detection_method()
    
    # 5. 总结和建议
    print("\n" + "=" * 70)
    print("分析总结和建议")
    print("=" * 70)
    
    print(f"\n📊 统计信息:")
    print(f"  - 找到相关包: {len(playstore_packages)} 个")
    print(f"  - 运行进程: {len(running_processes)} 个")
    print(f"  - 当前检测结果: {current_result}")
    
    # 确定真正的Play Store包名
    main_playstore = None
    if 'com.android.vending' in playstore_packages:
        main_playstore = 'com.android.vending'
    elif 'com.google.android.finsky' in playstore_packages:
        main_playstore = 'com.google.android.finsky'
    
    if main_playstore:
        print(f"\n✅ 确认的主要Play Store包名: {main_playstore}")
    else:
        print(f"\n❌ 未找到标准的Play Store包名")
    
    print(f"\n💡 建议:")
    print(f"  - 主要检测包名应该是: {main_playstore or '需要进一步确认'}")
    print(f"  - 建议同时检测Google服务相关包")
    print(f"  - 考虑检测后台服务进程")


if __name__ == "__main__":
    main()
