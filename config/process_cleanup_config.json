{"process_cleanup_config": {"common_user_apps": [{"package": "com.transsion.aivoiceassistant", "description": "Ella语音助手", "category": "system_app"}, {"package": "com.transsion.aivoiceassistant", "description": "Ella语音助手", "category": "system_app"}, {"package": "com.android.chrome", "description": "Chrome浏览器", "category": "browser"}, {"package": "com.facebook.katana", "description": "Facebook", "category": "social"}, {"package": "com.facebook.lite", "description": "Facebook Lite", "category": "social"}, {"package": "com.whatsapp", "description": "WhatsApp", "category": "social"}, {"package": "com.instagram.android", "description": "Instagram", "category": "social"}, {"package": "com.twitter.android", "description": "Twitter", "category": "social"}, {"package": "com.spotify.music", "description": "Spotify", "category": "media"}, {"package": "com.netflix.mediaclient", "description": "Netflix", "category": "media"}, {"package": "com.tencent.mm", "description": "微信", "category": "social"}, {"package": "com.alibaba.android.rimet", "description": "钉钉", "category": "work"}, {"package": "com.tencent.mobileqq", "description": "QQ", "category": "social"}, {"package": "com.sina.weibo", "description": "微博", "category": "social"}, {"package": "com.taobao.taobao", "description": "淘宝", "category": "shopping"}, {"package": "com.jingdong.app.mall", "description": "京东", "category": "shopping"}, {"package": "com.tencent.tmgp.sgame", "description": "王者荣耀", "category": "game"}, {"package": "com.tencent.ig", "description": "和平精英", "category": "game"}, {"package": "com.ss.android.ugc.aweme", "description": "抖音", "category": "media"}, {"package": "com.smile.gifmaker", "description": "快手", "category": "media"}, {"package": "com.google.android.apps.maps", "description": "google地图", "category": "maps"}, {"package": "com.UCMobile", "description": "UC浏览器", "category": "browser"}], "recent_apps_clear_positions": [{"x": 540, "y": 1800, "description": "底部中央清理按钮", "priority": 1}, {"x": 1000, "y": 1800, "description": "底部右侧清理按钮", "priority": 2}, {"x": 200, "y": 1800, "description": "底部左侧清理按钮", "priority": 3}, {"x": 540, "y": 200, "description": "顶部中央清理按钮", "priority": 4}, {"x": 1000, "y": 200, "description": "顶部右侧清理按钮", "priority": 5}, {"x": 800, "y": 1600, "description": "右下角清理按钮", "priority": 6}, {"x": 540, "y": 1600, "description": "中下清理按钮", "priority": 7}, {"x": 300, "y": 1600, "description": "左下清理按钮", "priority": 8}, {"x": 540, "y": 400, "description": "中上清理按钮", "priority": 9}, {"x": 900, "y": 1000, "description": "右中清理按钮", "priority": 10}], "cleanup_settings": {"command_cleanup_enabled": true, "recent_apps_fallback_enabled": true, "recent_apps_priority": false, "min_apps_for_fallback": 3, "cleanup_timeout": 8, "stabilization_wait": 2, "max_retry_attempts": 3, "force_stop_enabled": true, "recent_cleanup_wait": 3, "ui_detection_enabled": true, "enhanced_tap_enabled": true, "backup_methods_enabled": true, "page_load_wait": 2, "tap_interval": 0.8, "verification_enabled": true, "multi_gesture_enabled": true, "system_cleanup_enabled": true, "category_cleanup_enabled": true, "running_apps_detection": true}}}