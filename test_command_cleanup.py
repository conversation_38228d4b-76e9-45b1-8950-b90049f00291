#!/usr/bin/env python3
"""
测试命令清理功能的脚本
验证优化后的命令直接清理方法
"""

import sys
import os
import time
import subprocess
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from testcases.test_ella.base_ella_test import BaseEllaTest
from core.logger import log


def get_running_apps():
    """获取当前运行的应用列表"""
    running_apps = []
    try:
        # 方法1: 获取活动应用
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "activity", "activities", "|", "grep", "-E", "mResumedActivity|mFocusedActivity"],
            capture_output=True,
            text=True,
            timeout=10,
            shell=True
        )
        
        if result.returncode == 0 and result.stdout.strip():
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if 'ComponentInfo{' in line:
                    try:
                        start = line.find('ComponentInfo{') + len('ComponentInfo{')
                        end = line.find('/', start)
                        if start > 0 and end > start:
                            package_name = line[start:end]
                            if package_name and '.' in package_name:
                                running_apps.append(package_name)
                    except:
                        continue
        
        # 方法2: 获取Recent应用
        result2 = subprocess.run(
            ["adb", "shell", "dumpsys", "activity", "recents"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result2.returncode == 0:
            lines = result2.stdout.split('\n')
            for line in lines:
                if 'Task{' in line and 'A=' in line:
                    try:
                        start = line.find('A=') + 2
                        end = line.find(' ', start)
                        if start > 1 and end > start:
                            package_name = line[start:end]
                            if package_name and '.' in package_name and package_name not in running_apps:
                                running_apps.append(package_name)
                    except:
                        continue
        
        # 过滤系统应用
        filtered_apps = []
        system_keywords = ['android', 'system', 'launcher', 'inputmethod']
        for app in running_apps:
            is_system = any(keyword in app.lower() for keyword in system_keywords)
            if not is_system:
                filtered_apps.append(app)
        
        return filtered_apps
        
    except Exception as e:
        log.error(f"获取运行应用列表失败: {e}")
        return []


def start_test_apps():
    """启动一些测试应用"""
    test_apps = [
        ("com.google.android.apps.maps", "Google Maps"),
        ("com.android.chrome", "Chrome"),
        ("com.android.calculator2", "Calculator"),
        ("com.android.camera2", "Camera")
    ]
    
    started_apps = []
    for package, name in test_apps:
        try:
            result = subprocess.run(
                ["adb", "shell", "monkey", "-p", package, "-c", "android.intent.category.LAUNCHER", "1"],
                capture_output=True,
                text=True,
                timeout=8
            )
            if result.returncode == 0:
                started_apps.append((package, name))
                log.info(f"✅ 启动应用: {name} ({package})")
                time.sleep(1.5)
            else:
                log.warning(f"⚠️ 启动应用失败: {name}")
        except Exception as e:
            log.warning(f"⚠️ 启动 {name} 异常: {e}")
    
    return started_apps


def test_command_cleanup_methods():
    """测试各种命令清理方法"""
    log.info("🧪 测试各种命令清理方法...")
    
    base_test = BaseEllaTest()
    
    # 测试1: 停止运行应用列表
    log.info("\n--- 测试1: 停止运行应用列表 ---")
    before_apps = get_running_apps()
    log.info(f"清理前运行应用数量: {len(before_apps)}")
    
    cleared1 = base_test._stop_running_apps_by_list()
    log.info(f"方法1清理结果: {cleared1}")
    
    time.sleep(2)
    after_apps1 = get_running_apps()
    log.info(f"清理后运行应用数量: {len(after_apps1)}")
    
    # 测试2: 系统级清理
    log.info("\n--- 测试2: 系统级清理 ---")
    cleared2 = base_test._system_level_cleanup()
    log.info(f"方法2清理结果: {cleared2}")
    
    time.sleep(2)
    after_apps2 = get_running_apps()
    log.info(f"系统清理后运行应用数量: {len(after_apps2)}")
    
    # 测试3: 按类别清理
    log.info("\n--- 测试3: 按类别清理 ---")
    cleared3 = base_test._clear_apps_by_category()
    log.info(f"方法3清理结果: {cleared3}")
    
    time.sleep(2)
    after_apps3 = get_running_apps()
    log.info(f"类别清理后运行应用数量: {len(after_apps3)}")
    
    return {
        "before": len(before_apps),
        "after_method1": len(after_apps1),
        "after_method2": len(after_apps2),
        "after_method3": len(after_apps3),
        "cleared_counts": [cleared1, cleared2, cleared3]
    }


def test_complete_command_cleanup():
    """测试完整的命令清理流程"""
    log.info("🚀 测试完整的命令清理流程...")
    
    try:
        # 1. 启动测试应用
        log.info("步骤1: 启动测试应用...")
        started_apps = start_test_apps()
        if not started_apps:
            log.warning("没有成功启动任何测试应用")
            return False
        
        time.sleep(3)
        
        # 2. 检查清理前的应用状态
        log.info("步骤2: 检查清理前的应用状态...")
        before_apps = get_running_apps()
        log.info(f"清理前运行应用数量: {len(before_apps)}")
        for app in before_apps[:10]:  # 只显示前10个
            log.info(f"  - {app}")
        
        # 3. 执行命令清理
        log.info("步骤3: 执行命令清理...")
        base_test = BaseEllaTest()
        cleared_count = base_test._command_clear_all_apps()
        log.info(f"命令清理返回值: {cleared_count}")
        
        time.sleep(3)
        
        # 4. 检查清理后的应用状态
        log.info("步骤4: 检查清理后的应用状态...")
        after_apps = get_running_apps()
        log.info(f"清理后运行应用数量: {len(after_apps)}")
        for app in after_apps[:10]:  # 只显示前10个
            log.info(f"  - {app}")
        
        # 5. 评估清理效果
        if len(before_apps) > 0:
            cleared_actual = len(before_apps) - len(after_apps)
            if cleared_actual > 0:
                log.info(f"✅ 命令清理成功! 实际清理了 {cleared_actual} 个应用")
                return True
            elif len(after_apps) == 0:
                log.info("✅ 命令清理成功! 所有应用都被清理")
                return True
            else:
                log.warning(f"⚠️ 命令清理效果有限，应用数量变化: {len(before_apps)} -> {len(after_apps)}")
                return cleared_count > 0
        else:
            log.info("ℹ️ 清理前没有运行应用，无法评估效果")
            return True
        
    except Exception as e:
        log.error(f"完整命令清理测试异常: {e}")
        return False


def test_fallback_to_recent():
    """测试降级到Recent清理的情况"""
    log.info("🔄 测试降级到Recent清理...")
    
    try:
        # 启动应用
        started_apps = start_test_apps()
        time.sleep(3)
        
        # 模拟命令清理效果不佳的情况（通过修改配置）
        base_test = BaseEllaTest()
        
        # 执行完整清理流程
        log.info("执行完整清理流程（包含降级逻辑）...")
        base_test.clear_all_running_processes()
        
        time.sleep(3)
        
        # 检查最终效果
        final_apps = get_running_apps()
        log.info(f"最终运行应用数量: {len(final_apps)}")
        
        return len(final_apps) < 5  # 如果最终应用数量少于5个，认为清理成功
        
    except Exception as e:
        log.error(f"降级测试异常: {e}")
        return False


def main():
    """主函数"""
    log.info("🚀 开始测试命令清理功能...")
    
    # 检查ADB连接
    try:
        result = subprocess.run(
            ["adb", "devices"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if "device" not in result.stdout:
            log.error("❌ 未检测到ADB设备连接")
            return
        else:
            log.info("✅ ADB设备连接正常")
            
    except Exception as e:
        log.error(f"❌ ADB连接检查失败: {e}")
        return
    
    # 测试序列
    tests = [
        ("各种命令清理方法", test_command_cleanup_methods),
        ("完整命令清理流程", test_complete_command_cleanup),
        ("降级到Recent清理", test_fallback_to_recent)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        log.info(f"\n{'='*60}")
        log.info(f"测试: {test_name}")
        log.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            if result:
                log.info(f"✅ {test_name} 测试通过")
            else:
                log.warning(f"⚠️ {test_name} 测试失败")
        except Exception as e:
            log.error(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
        
        time.sleep(5)  # 测试间隔
    
    # 输出测试总结
    log.info(f"\n{'='*60}")
    log.info("测试总结")
    log.info(f"{'='*60}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        log.info(f"{test_name}: {status}")
    
    log.info(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        log.info("🎉 所有测试都通过了！命令清理功能工作正常")
    elif passed > 0:
        log.info("⚠️ 部分测试通过，命令清理功能部分可用")
    else:
        log.error("❌ 所有测试都失败了，需要进一步优化")


if __name__ == "__main__":
    main()
