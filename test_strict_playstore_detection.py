#!/usr/bin/env python3
"""
测试严格的Google Play Store检测功能
"""
import sys
import os
import subprocess
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pages.base.app_detector import AppDetector
from core.logger import log


def check_actual_vending_status():
    """检查实际的vending状态"""
    print("🔍 检查实际vending状态...")
    
    # 1. 检查ps进程
    try:
        result = subprocess.run(
            ["adb", "shell", "ps"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            ps_output = result.stdout
            vending_lines = [line for line in ps_output.split('\n') if 'vending' in line]
            
            print(f"  📊 ps命令结果:")
            if vending_lines:
                print(f"    找到 {len(vending_lines)} 个vending相关行:")
                for line in vending_lines:
                    print(f"      {line.strip()}")
            else:
                print(f"    ❌ 未找到vending进程")
        else:
            print(f"  ❌ ps命令失败")
    except Exception as e:
        print(f"  ❌ ps检查失败: {e}")
    
    # 2. 检查pidof
    try:
        result = subprocess.run(
            ["adb", "shell", "pidof", "com.android.vending"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        print(f"  📊 pidof结果:")
        if result.returncode == 0 and result.stdout.strip():
            print(f"    ✅ 找到PID: {result.stdout.strip()}")
        else:
            print(f"    ❌ 未找到PID")
    except Exception as e:
        print(f"  ❌ pidof检查失败: {e}")
    
    # 3. 检查dumpsys activity processes
    try:
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "activity", "processes"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            processes_output = result.stdout
            vending_lines = [line for line in processes_output.split('\n') if 'vending' in line]
            
            print(f"  📊 dumpsys activity processes结果:")
            if vending_lines:
                print(f"    找到 {len(vending_lines)} 个vending相关行:")
                for line in vending_lines:
                    print(f"      {line.strip()}")
                    if "DEAD" in line:
                        print(f"        ⚠️  这是一个DEAD连接，不应被视为活跃进程")
            else:
                print(f"    ❌ 未找到vending相关信息")
        else:
            print(f"  ❌ dumpsys命令失败")
    except Exception as e:
        print(f"  ❌ dumpsys检查失败: {e}")


def test_detection_before_after():
    """测试检测方法在启动前后的表现"""
    print("\n🔍 测试检测方法在启动前后的表现...")
    
    detector = AppDetector()
    
    # 1. 检测当前状态（应该是未启动）
    print("  📊 步骤1: 检测当前状态")
    current_result = detector.check_google_playstore_app_opened()
    print(f"    当前检测结果: {current_result}")
    
    # 2. 启动Play Store
    print("  📊 步骤2: 启动Play Store")
    try:
        subprocess.run(
            ["adb", "shell", "am", "start", "-n", "com.android.vending/.AssetBrowserActivity"],
            capture_output=True,
            text=True,
            timeout=10
        )
        print("    已发送启动命令")
        
        # 等待启动
        time.sleep(5)
        
        # 检测启动后状态
        after_start_result = detector.check_google_playstore_app_opened()
        print(f"    启动后检测结果: {after_start_result}")
        
    except Exception as e:
        print(f"    启动失败: {e}")
        after_start_result = None
    
    # 3. 强制停止Play Store
    print("  📊 步骤3: 强制停止Play Store")
    try:
        subprocess.run(
            ["adb", "shell", "am", "force-stop", "com.android.vending"],
            capture_output=True,
            text=True,
            timeout=10
        )
        print("    已执行强制停止")
        
        # 等待停止
        time.sleep(3)
        
        # 检测停止后状态
        after_stop_result = detector.check_google_playstore_app_opened()
        print(f"    停止后检测结果: {after_stop_result}")
        
    except Exception as e:
        print(f"    停止失败: {e}")
        after_stop_result = None
    
    # 4. 总结
    print(f"  📊 测试总结:")
    print(f"    初始状态: {current_result}")
    print(f"    启动后: {after_start_result}")
    print(f"    停止后: {after_stop_result}")
    
    # 验证逻辑
    if current_result == False and after_start_result == True and after_stop_result == False:
        print(f"    ✅ 检测逻辑完全正确")
    elif current_result == False and after_stop_result == False:
        print(f"    ✅ 检测逻辑基本正确（未启动时正确返回False）")
    else:
        print(f"    ⚠️  检测逻辑需要进一步优化")
    
    return {
        'initial': current_result,
        'after_start': after_start_result,
        'after_stop': after_stop_result
    }


def test_multiple_detections():
    """测试多次检测的一致性"""
    print("\n🔍 测试多次检测的一致性...")
    
    detector = AppDetector()
    results = []
    
    for i in range(3):
        result = detector.check_google_playstore_app_opened()
        results.append(result)
        print(f"  第{i+1}次检测: {result}")
        time.sleep(1)
    
    consistent = len(set(results)) == 1
    print(f"  📊 一致性: {consistent}")
    print(f"  📊 结果集合: {set(results)}")
    
    return consistent


def main():
    """主函数"""
    print("=" * 70)
    print("严格的Google Play Store检测功能测试")
    print("=" * 70)
    
    # 1. 检查实际状态
    check_actual_vending_status()
    
    # 2. 测试检测方法
    detection_results = test_detection_before_after()
    
    # 3. 测试一致性
    consistency = test_multiple_detections()
    
    # 4. 最终总结
    print("\n" + "=" * 70)
    print("测试总结")
    print("=" * 70)
    
    print(f"\n📊 检测结果分析:")
    if detection_results:
        for phase, result in detection_results.items():
            print(f"  - {phase}: {result}")
    
    print(f"\n📊 检测质量:")
    print(f"  - 一致性: {consistency}")
    
    # 判断优化效果
    if detection_results and detection_results.get('initial') == False:
        print(f"\n✅ 优化成功!")
        print(f"  - Play Store未启动时正确返回False")
        print(f"  - 检测逻辑已修复误判问题")
    else:
        print(f"\n⚠️  仍需进一步优化")
        print(f"  - Play Store未启动时仍返回True")
        print(f"  - 需要检查其他可能的误判源")


if __name__ == "__main__":
    main()
