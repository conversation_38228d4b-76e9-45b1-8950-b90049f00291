#!/usr/bin/env python3
"""
测试优化后的Google Play Store检测功能
"""
import sys
import os
import subprocess
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pages.base.app_detector import AppDetector
from core.logger import log


def check_actual_vending_processes():
    """检查实际的vending相关进程"""
    print("🔍 检查实际的vending相关进程...")
    
    try:
        result = subprocess.run(
            ["adb", "shell", "ps"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            ps_output = result.stdout
            vending_processes = []
            
            for line in ps_output.split('\n'):
                if 'vending' in line:
                    vending_processes.append(line.strip())
            
            if vending_processes:
                print(f"  找到 {len(vending_processes)} 个vending进程:")
                for process in vending_processes:
                    print(f"    📋 {process}")
                return True
            else:
                print("  ❌ 未找到vending进程")
                return False
        else:
            print("  ❌ 获取进程列表失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return False


def check_google_services_processes():
    """检查Google服务相关进程"""
    print("\n🔍 检查Google服务相关进程...")
    
    try:
        result = subprocess.run(
            ["adb", "shell", "ps"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            ps_output = result.stdout
            gms_processes = []
            
            for line in ps_output.split('\n'):
                if 'google.android.gms' in line:
                    gms_processes.append(line.strip())
            
            if gms_processes:
                print(f"  找到 {len(gms_processes)} 个Google服务进程:")
                for process in gms_processes:
                    print(f"    📋 {process}")
                return True
            else:
                print("  ❌ 未找到Google服务进程")
                return False
        else:
            print("  ❌ 获取进程列表失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return False


def test_optimized_detection():
    """测试优化后的检测方法"""
    print("\n🔍 测试优化后的检测方法...")
    
    detector = AppDetector()
    
    try:
        result = detector.check_google_playstore_app_opened()
        
        if result:
            print("✅ 检测结果: Google Play Store相关进程正在运行")
        else:
            print("❌ 检测结果: Google Play Store相关进程未运行")
            
        return result
    except Exception as e:
        print(f"❌ 检测失败: {e}")
        return False


def test_force_stop_scenario():
    """测试强制停止场景"""
    print("\n🔍 测试强制停止Play Store后的检测...")
    
    detector = AppDetector()
    
    try:
        # 1. 检测当前状态
        print("  📊 步骤1: 检测当前状态")
        result1 = detector.check_google_playstore_app_opened()
        print(f"    当前状态: {result1}")
        
        # 2. 强制停止Play Store
        print("  📊 步骤2: 强制停止Play Store")
        subprocess.run(
            ["adb", "shell", "am", "force-stop", "com.android.vending"],
            capture_output=True,
            text=True,
            timeout=10
        )
        print("    已执行强制停止命令")
        
        # 等待停止完成
        time.sleep(3)
        
        # 3. 检测停止后状态
        print("  📊 步骤3: 检测停止后状态")
        result2 = detector.check_google_playstore_app_opened()
        print(f"    停止后状态: {result2}")
        
        # 4. 手动检查vending进程
        print("  📊 步骤4: 手动检查vending进程")
        manual_check = check_actual_vending_processes()
        
        # 5. 启动Play Store
        print("  📊 步骤5: 重新启动Play Store")
        subprocess.run(
            ["adb", "shell", "am", "start", "-n", "com.android.vending/.AssetBrowserActivity"],
            capture_output=True,
            text=True,
            timeout=10
        )
        print("    已发送启动命令")
        
        # 等待启动完成
        time.sleep(5)
        
        # 6. 检测启动后状态
        print("  📊 步骤6: 检测启动后状态")
        result3 = detector.check_google_playstore_app_opened()
        print(f"    启动后状态: {result3}")
        
        # 总结
        print(f"\n  📊 测试总结:")
        print(f"    初始状态: {result1}")
        print(f"    强制停止后: {result2}")
        print(f"    手动检查: {manual_check}")
        print(f"    重新启动后: {result3}")
        
        return {
            'initial': result1,
            'after_stop': result2,
            'manual_check': manual_check,
            'after_restart': result3
        }
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return None


def main():
    """主测试函数"""
    print("=" * 70)
    print("优化后的Google Play Store检测测试")
    print("=" * 70)
    
    # 1. 检查实际进程状态
    vending_exists = check_actual_vending_processes()
    gms_exists = check_google_services_processes()
    
    # 2. 测试优化后的检测方法
    detection_result = test_optimized_detection()
    
    # 3. 测试强制停止场景
    force_stop_results = test_force_stop_scenario()
    
    # 4. 总结
    print("\n" + "=" * 70)
    print("测试总结")
    print("=" * 70)
    
    print(f"\n📊 进程检查结果:")
    print(f"  - Vending进程存在: {vending_exists}")
    print(f"  - Google服务进程存在: {gms_exists}")
    print(f"  - 优化后检测结果: {detection_result}")
    
    if force_stop_results:
        print(f"\n📊 强制停止测试结果:")
        for key, value in force_stop_results.items():
            print(f"  - {key}: {value}")
    
    # 验证检测准确性
    if vending_exists == detection_result:
        print(f"\n✅ 检测准确性: 优化后的检测方法与实际进程状态一致")
    else:
        print(f"\n⚠️  检测准确性: 检测结果与实际状态不一致，需要进一步调试")
    
    print(f"\n💡 优化建议:")
    if vending_exists:
        print(f"  - 当前设备确实有Play Store进程运行")
        print(f"  - 检测方法应该重点关注vending相关进程")
    else:
        print(f"  - 当前设备可能没有活跃的Play Store进程")
        print(f"  - 可以考虑检测Google服务作为备用指标")


if __name__ == "__main__":
    main()
