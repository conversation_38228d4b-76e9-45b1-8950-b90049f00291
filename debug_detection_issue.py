#!/usr/bin/env python3
"""
调试检测问题
"""
import sys
import os
import subprocess

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pages.base.app_detector import AppDetector
from core.logger import log


def debug_ps_output():
    """调试ps输出"""
    print("🔍 调试ps输出...")
    
    try:
        result = subprocess.run(
            ["adb", "shell", "ps"],
            capture_output=True,
            text=True,
            timeout=10,
            encoding='utf-8',
            errors='ignore'
        )
        
        if result.returncode == 0:
            ps_output = result.stdout
            lines = ps_output.split('\n')
            
            print(f"  📊 ps输出总行数: {len(lines)}")
            
            # 查找包含vending的行
            vending_lines = []
            for i, line in enumerate(lines):
                if 'vending' in line.lower():
                    vending_lines.append((i, line))
            
            if vending_lines:
                print(f"  📊 包含vending的行数: {len(vending_lines)}")
                for line_num, line in vending_lines:
                    print(f"    行{line_num}: {repr(line)}")
                    
                    # 分析行结构
                    parts = line.split()
                    print(f"      字段数: {len(parts)}")
                    if parts:
                        print(f"      字段: {parts}")
            else:
                print(f"  ❌ 未找到包含vending的行")
                
            # 检查前几行和后几行
            print(f"\n  📊 ps输出前5行:")
            for i, line in enumerate(lines[:5]):
                print(f"    行{i}: {repr(line)}")
                
        else:
            print(f"  ❌ ps命令失败: {result.stderr}")
            
    except Exception as e:
        print(f"  ❌ 调试失败: {e}")


def debug_detection_methods():
    """调试各个检测方法"""
    print("\n🔍 调试各个检测方法...")
    
    # 模拟检测方法的各个步骤
    vending_patterns = [
        "com.android.vending",
        "com.android.vending:background", 
        "com.android.vending:quick_launch",
    ]
    
    # 方法1: ps命令
    print("  📊 方法1: ps命令")
    try:
        result = subprocess.run(
            ["adb", "shell", "ps"],
            capture_output=True,
            text=True,
            timeout=10,
            encoding='utf-8',
            errors='ignore'
        )
        
        if result.returncode == 0:
            ps_output = result.stdout
            lines = ps_output.split('\n')
            
            found_in_method1 = False
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                for pattern in vending_patterns:
                    if pattern in line:
                        parts = line.split()
                        if len(parts) >= 8:
                            if any(status in parts for status in ['S', 'R', 'D', 'T', 'Z']):
                                print(f"    ✅ 找到: {pattern}")
                                print(f"    行: {line}")
                                found_in_method1 = True
                                break
            
            if not found_in_method1:
                print(f"    ❌ 方法1未找到vending进程")
        else:
            print(f"    ❌ 方法1失败")
    except Exception as e:
        print(f"    ❌ 方法1异常: {e}")
    
    # 方法2: ps -A命令
    print("  📊 方法2: ps -A命令")
    try:
        result = subprocess.run(
            ["adb", "shell", "ps", "-A"],
            capture_output=True,
            text=True,
            timeout=10,
            encoding='utf-8',
            errors='ignore'
        )
        
        if result.returncode == 0:
            ps_a_output = result.stdout
            if "com.android.vending" in ps_a_output:
                print(f"    ✅ 方法2找到vending")
                # 显示相关行
                lines = ps_a_output.split('\n')
                for line in lines:
                    if 'vending' in line:
                        print(f"    行: {line.strip()}")
            else:
                print(f"    ❌ 方法2未找到vending")
        else:
            print(f"    ❌ 方法2失败")
    except Exception as e:
        print(f"    ❌ 方法2异常: {e}")
    
    # 方法3: dumpsys activity processes
    print("  📊 方法3: dumpsys activity processes")
    try:
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "activity", "processes"],
            capture_output=True,
            text=True,
            timeout=10,
            encoding='utf-8',
            errors='ignore'
        )
        
        if result.returncode == 0:
            processes_output = result.stdout
            if "com.android.vending" in processes_output:
                print(f"    ✅ 方法3找到vending")
                lines = processes_output.split('\n')
                for line in lines:
                    if "com.android.vending" in line:
                        if "DEAD" not in line and "ConnectionRecord" not in line:
                            print(f"    活跃行: {line.strip()}")
                        else:
                            print(f"    非活跃行: {line.strip()}")
            else:
                print(f"    ❌ 方法3未找到vending")
        else:
            print(f"    ❌ 方法3失败")
    except Exception as e:
        print(f"    ❌ 方法3异常: {e}")


def test_actual_detection():
    """测试实际的检测方法"""
    print("\n🔍 测试实际的检测方法...")
    
    detector = AppDetector()
    
    # 启用更详细的日志
    import logging
    logging.getLogger().setLevel(logging.DEBUG)
    
    result = detector.check_google_playstore_app_opened()
    print(f"  📊 检测结果: {result}")


def main():
    """主函数"""
    print("=" * 70)
    print("调试Google Play Store检测问题")
    print("=" * 70)
    
    # 1. 调试ps输出
    debug_ps_output()
    
    # 2. 调试各个检测方法
    debug_detection_methods()
    
    # 3. 测试实际检测
    test_actual_detection()


if __name__ == "__main__":
    main()
